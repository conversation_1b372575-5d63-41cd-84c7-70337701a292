// Application Service
import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, ILike } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSApplication } from '~/common/enums';
import { systemHelper } from '~/common/helpers';
import { ApplicationRepo, TenantApplicationRepo, TenantRepo } from '~/domains/primary';
import {
    ApplicationListDto,
    CreateApplicationDto,
    UpdateApplicationDto,
} from './dto/application.dto';

@Injectable()
export class ApplicationService {
    constructor(
        @InjectRepo(ApplicationRepo)
        readonly applicationRepo: ApplicationRepo,
        @InjectRepo(TenantRepo)
        readonly tenantRepo: TenantRepo,
        @InjectRepo(TenantApplicationRepo)
        readonly tenantApplicationRepo: TenantApplicationRepo,
    ) {}

    async list(params: ApplicationListDto) {
        const {
            createdDateFrom,
            createdDateTo,
            pageIndex,
            pageSize,
            searchValue,
            code,
            name,
            status,
        } = params;

        const otherFilters: any = {};

        if (status) {
            otherFilters.status = status;
        }

        if (createdDateFrom && createdDateTo) {
            otherFilters.createdDate = Between(new Date(createdDateFrom), new Date(createdDateTo));
        }

        let where: any;
        if (searchValue) {
            where = [
                { code: ILike(`%${searchValue}%`), ...otherFilters },
                { name: ILike(`%${searchValue}%`), ...otherFilters },
            ];
        } else {
            if (code) otherFilters.code = ILike(`%${code}%`);
            if (name) otherFilters.name = ILike(`%${name}%`);
            where = otherFilters;
        }

        const { data, total } = await this.applicationRepo.findPagination(
            {
                where,
                order: { createdDate: 'DESC' },
            },
            {
                pageIndex,
                pageSize,
            },
        );

        const tenantApplicationLst = await this.tenantApplicationRepo.find();
        const tenantLst = await this.tenantRepo.find();

        const mappingData = data.map(item => {
            const tenants = tenantApplicationLst
                .filter(ta => ta.applicationId === item.id)
                .map(ta => tenantLst.find(t => t.id === ta.tenantId))
                .filter(t => t);
            return {
                ...item,
                tenants,
            };
        });

        return {
            data: mappingData,
            total,
        };
    }

    @DefTransaction()
    async create(body: CreateApplicationDto) {
        return this.applicationRepo.save({
            ...body,
            status: NSApplication.EStatus.ACTIVE,
            clientId: systemHelper.generateClientId(body.code),
            clientSecret: systemHelper.generateClientSecret(),
        });
    }

    @DefTransaction()
    async update(body: UpdateApplicationDto) {
        delete body.clientId; // Prevent updating clientId
        delete body.clientSecret; // Prevent updating clientSecret
        const app = await this.applicationRepo.findOne({ where: { id: body.id } });
        if (!app) {
            throw new BusinessException('Application not found');
        }
        return this.applicationRepo.save(body);
    }

    @DefTransaction()
    async inactive(id: string) {
        const app = await this.applicationRepo.findOne({ where: { id } });
        if (!app) {
            throw new BusinessException('Application not found');
        }
        app.status = NSApplication.EStatus.INACTIVE;
        return this.applicationRepo.save(app);
    }

    @DefTransaction()
    async active(id: string) {
        const app = await this.applicationRepo.findOne({ where: { id } });
        if (!app) {
            throw new BusinessException('Application not found');
        }
        app.status = NSApplication.EStatus.ACTIVE;
        return this.applicationRepo.save(app);
    }
}
