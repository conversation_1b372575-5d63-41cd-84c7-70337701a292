# Primary Domain - Database Tables

Đây là tài liệu mô tả các bảng cơ sở dữ liệu trong domain chính của hệ thống xác thực APE Authenticator API.

## Tổng quan

Domain primary chứa các bảng cốt lõi của hệ thống xác thực, bao gồ<PERSON> quản lý tenant, t<PERSON><PERSON>, <PERSON><PERSON>, phân quyền và các token xác thực.

## Danh sách các bảng

### 1. `tenant` - <PERSON><PERSON><PERSON> Tenant (Công ty/Tổ chức)

**Mục đích**: <PERSON><PERSON><PERSON> trữ thông tin các công ty/tổ chức sử dụng hệ thống.

**Các trường chính**:

- `id`: ID duy nhất của tenant
- `name`: Tên hiển thị của tenant
- `domain`: Mã doanh nghiệ<PERSON> (unique)
- `logoUrl`: Đường dẫn logo công ty
- `status`: Trạng thái hoạt động (mặc định: ACTIVE)
- `address`: Địa chỉ công ty
- `website`: Website công ty
- `phone`: Số điện thoại
- `email`: Email liên hệ
- `description`: Mô tả về công ty
- `taxCode`: Mã số thuế

**Ghi chú**: Đây là bảng trung tâm của hệ thống multi-tenant, mỗi tenant đại diện cho một công ty/tổ chức riêng biệt.

### 2. `account` - Bảng Tài khoản

**Mục đích**: Lưu trữ thông tin tài khoản người dùng trong hệ thống.

**Các trường chính**:

- `id`: ID duy nhất của tài khoản
- `tenantId`: ID của tenant mà tài khoản thuộc về
- `username`: Tên đăng nhập (unique)
- `password`: Mật khẩu đã được mã hóa
- `fullName`: Họ tên đầy đủ
- `avatar`: Đường dẫn ảnh đại diện
- `type`: Loại tài khoản (enum NSAccount.EType) - VD: "TENANT_MASTER", "TENANT_USER", "CUSTOMER"
- `status`: Trạng thái tài khoản (enum NSAccount.EStatus) - VD: "ACTIVE", "INACTIVE", "LOCKED"

**Ghi chú**: Mỗi tài khoản thuộc về một tenant cụ thể, hỗ trợ mô hình multi-tenant.

### 3. `application` - Bảng Ứng dụng

**Mục đích**: Lưu trữ thông tin các ứng dụng có thể tích hợp với hệ thống xác thực.

**Các trường chính**:

- `id`: ID duy nhất của ứng dụng
- `code`: Mã ứng dụng (unique) - VD: "CRM", "APE_CHAIN"
- `name`: Tên hiển thị của ứng dụng
- `clientId`: Client ID cho OAuth (unique)
- `clientSecret`: Client Secret cho OAuth (unique)
- `redirectUris`: Danh sách các URI redirect được phép
- `description`: Mô tả ứng dụng
- `status`: Trạng thái ứng dụng (enum NSApplication.EStatus) - VD: "ACTIVE", "INACTIVE"
- `allowedAccountTypes`: Loại tài khoản được phép truy cập, nếu null hoặc mảng rỗng thì là cho tất cả truy cập (enum NSAccount.EType) - VD: "TENANT_MASTER", "TENANT_USER", "CUSTOMER"

**Ghi chú**: Hỗ trợ OAuth 2.0 flow, mỗi ứng dụng có thể tích hợp với nhiều tenant.

### 4. `role` - Bảng Vai trò

**Mục đích**: Định nghĩa các vai trò/quyền hạn trong hệ thống.

**Các trường chính**:

- `id`: ID duy nhất của vai trò
- `tenantId`: ID của tenant mà vai trò thuộc về
- `name`: Tên vai trò
- `description`: Mô tả vai trò

**Ghi chú**: Vai trò được định nghĩa theo từng tenant, cho phép mỗi công ty tự định nghĩa hệ thống phân quyền riêng.

### 5. `account_role` - Bảng Liên kết Tài khoản - Vai trò

**Mục đích**: Bảng trung gian liên kết tài khoản với các vai trò (many-to-many relationship).

**Các trường chính**:

- `accountId`: ID của tài khoản (Primary Key)
- `roleId`: ID của vai trò (Primary Key)

**Ghi chú**: Sử dụng composite primary key, một tài khoản có thể có nhiều vai trò và một vai trò có thể được gán cho nhiều tài khoản.

### 6. `tenant_application` - Bảng Liên kết Tenant - Ứng dụng

**Mục đích**: Quản lý quyền truy cập của các tenant vào các ứng dụng cụ thể.

**Các trường chính**:

- `id`: ID duy nhất
- `tenantId`: ID của tenant (Primary Key)
- `applicationId`: ID của ứng dụng (Primary Key)
- `clientId`: Client ID riêng cho tenant-app này
- `clientSecret`: Client Secret riêng cho tenant-app này
- `redirectUris`: Danh sách URI redirect được phép cho tenant này
- `isActive`: Trạng thái kích hoạt
- `plan`: Gói dịch vụ (mặc định: "default")

**Ghi chú**: Cho phép mỗi tenant có cấu hình OAuth riêng khi truy cập vào từng ứng dụng, hỗ trợ tích hợp với các công ty bên ngoài.

### 7. `session` - Bảng Phiên đăng nhập

**Mục đích**: Theo dõi các phiên đăng nhập của người dùng.

**Các trường chính**:

- `id`: ID duy nhất của phiên
- `accountId`: ID của tài khoản
- `userAgent`: Thông tin trình duyệt/thiết bị
- `ip`: Địa chỉ IP đăng nhập
- `loginAt`: Thời gian đăng nhập
- `logoutAt`: Thời gian đăng xuất (nullable)

**Ghi chú**: Hỗ trợ audit trail và quản lý phiên đăng nhập, có thể theo dõi các phiên đang hoạt động.

### 8. `authorization_code` - Bảng Mã xác thực

**Mục đích**: Lưu trữ authorization codes trong OAuth 2.0 flow.

**Các trường chính**:

- `id`: ID duy nhất
- `accountId`: ID của tài khoản
- `clientId`: Client ID của ứng dụng
- `code`: Mã xác thực
- `expiresAt`: Thời gian hết hạn
- `redirectUri`: URI redirect
- `scope`: Danh sách các quyền được yêu cầu

**Ghi chú**: Mã xác thực có thời gian sống ngắn, được sử dụng để đổi lấy access token trong OAuth flow.

### 9. `token` - Bảng Token

**Mục đích**: Lưu trữ access tokens và refresh tokens.

**Các trường chính**:

- `id`: ID duy nhất
- `accountId`: ID của tài khoản
- `clientId`: Client ID của ứng dụng
- `accessToken`: Access token (500 ký tự)
- `refreshToken`: Refresh token (500 ký tự)
- `expiresAt`: Thời gian hết hạn của access token
- `scope`: Danh sách các quyền được cấp

**Ghi chú**: Access token có thời gian sống giới hạn, refresh token được sử dụng để gia hạn access token mà không cần đăng nhập lại.

## Mối quan hệ giữa các bảng

```
tenant (1) -----> (n) account
tenant (1) -----> (n) role
tenant (n) <----> (n) application (thông qua tenant_application)
account (n) <----> (n) role (thông qua account_role)
account (1) -----> (n) session
account (1) -----> (n) authorization_code
account (1) -----> (n) token
```

## Lưu ý kỹ thuật

- Tất cả các bảng đều kế thừa từ `PrimaryBaseEntity` với các trường cơ bản như `id`, `createdDate`, `updatedDate`, `createdBy`, `updatedBy`, `version`
- Hệ thống hỗ trợ multi-tenant architecture
- Tuân thủ chuẩn OAuth 2.0 cho authentication và authorization
- Sử dụng UUID cho các khóa chính và khóa ngoại
- Hỗ trợ soft delete và audit trail thông qua base entity
