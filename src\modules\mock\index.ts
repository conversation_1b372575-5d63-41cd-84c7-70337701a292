import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule, lazyLoadClasses } from 'nestjs-typeorm3-kit';
import { PREFIX_MODULE } from '../config-module';
import { join } from 'path';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';

const controllers = lazyLoadClasses(join(__dirname), ['.controller']);
const services = lazyLoadClasses(join(__dirname), ['.service']);

@ChildModule({
    prefix: PREFIX_MODULE.mock,
    imports: [PrimaryRepoModule],
    providers: [...services],
    controllers: [...controllers],
})
export class MockModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
