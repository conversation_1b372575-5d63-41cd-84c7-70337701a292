#ConfigMap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ape-authenticator-api-dev-config
  namespace: ape-authenticator-dev
data:
  NODE_ENV: 'production'
  PORT: '80'
  TZ: 'UTC'
  REQUEST_TIMEOUT: '180000'
  #Swagger Config
  SWAGGER_TITLE: 'APE AUTHENTICATOR API'
  SWAGGER_DESCRIPTION: 'The APE AUTHENTICATOR API'
  SWAGGER_VERSION: '1.0'
  # Primary Database
  DB_PRIMARY_TYPE: 'postgres'
  DB_PRIMARY_HOST: 'ape-postgre.c7uzjfmteanl.ap-southeast-1.rds.amazonaws.com'
  DB_PRIMARY_PORT: '5432'
  DB_PRIMARY_USERNAME: 'ape-authenticator-dev'
  DB_PRIMARY_PASSWORD: 'apeA#@uthenti%cator'
  DB_PRIMARY_DATABASE: 'ape-authenticator-dev'
  DB_PRIMARY_SYNCHRONIZE: 'true'
  DB_PRIMARY_SSL: 'true'
  DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: 'false'
  # JWT HS256 config
  JWT_SECRET: '/q5zjNG6W0cbEdseJEySMY7xrN/5BVCK5j/CaILyRvo'
  JWT_EXPIRY: '100d'
  JWT_REFRESH_TOKEN_SECRET: '/A5zjN26W0cbEdseJEDsMY7xrN/5BVCK5j/ZolUyYbi'
  JWT_REFRESH_TOKEN_EXPIRY: '300d'
  #API KEY
  PUBLIC_API_KEY: '$yHvX4EN&i*GC_V()DFER4A%TR!Vx4Ya#GOq@jyL<DsXFawsS(j=txdj12KEw@'
---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-authenticator-api-dev
  namespace: ape-authenticator-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-authenticator-api-dev
  template:
    metadata:
      labels:
        app: ape-authenticator-api-dev
    spec:
      containers:
        - name: ape-authenticator-api-dev
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-authenticator-api-dev:latest
          ports:
            - containerPort: 80
          envFrom:
            - configMapRef:
                name: ape-authenticator-api-dev-config
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ape-authenticator-api-dev
  namespace: ape-authenticator-dev
  labels:
    run: ape-authenticator-api-dev
spec:
  type: ClusterIP
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: ape-authenticator-api-dev
