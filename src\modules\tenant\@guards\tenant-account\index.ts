import { Module } from '@nestjs/common';
import { TenantAccountGuard } from './tenant-account.guard';
import { TenantAccountStrategy } from './tenant-account.strategy';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';

@Module({
    imports: [PrimaryRepoModule],
    providers: [TenantAccountGuard, TenantAccountStrategy],
    exports: [TenantAccountGuard],
})
export class TenantAccountModule {}
