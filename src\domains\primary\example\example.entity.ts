import { Entity, Column } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { ApiPropertyOptional } from '@nestjs/swagger';

@Entity('example')
export class ExampleEntity extends PrimaryBaseEntity {
    // TEXT
    @ApiPropertyOptional({
        description: 'Text value',
    })
    @Column({ type: 'varchar', length: 255, default: '' })
    stringValue?: string;

    // NUMBER
    @ApiPropertyOptional({
        description: 'Number value',
    })
    @Column({ type: 'int', default: 0 })
    numberValue?: number;

    // BOOLEAN
    @ApiPropertyOptional({
        description: 'Boolean value',
    })
    @Column({ type: 'boolean', default: false })
    booleanValue?: boolean;

    // DATE
    @ApiPropertyOptional({
        description: 'Date value',
    })
    @Column({ type: 'date', default: () => 'CURRENT_DATE' })
    dateValue?: Date;

    // DATETIME (with timezone)
    @ApiPropertyOptional({
        description: 'Datetime value',
    })
    @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
    dateTimeValue?: Date;

    @ApiPropertyOptional({
        description: 'Select values',
    })
    @Column({
        type: 'varchar',
        array: true,
        default: () => "'{}'",
    })
    selectValues?: string[];

    @ApiPropertyOptional({
        description: 'JSON object value',
    })
    @Column({ type: 'jsonb', default: () => "'{}'" })
    jsonObjectValue?: Record<string, any>;

    @ApiPropertyOptional({
        description: 'JSON array value',
    })
    @Column({ type: 'jsonb', default: () => "'[]'" })
    jsonArrayValue?: any[];
}
