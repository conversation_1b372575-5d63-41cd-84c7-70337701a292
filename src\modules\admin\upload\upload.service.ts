import { configEnv } from '~/@config/env';
import { Injectable } from '@nestjs/common';
import { nanoid } from 'nanoid';
import * as AWS from 'aws-sdk';

@Injectable()
export class UploadService {
    AWS_S3_BUCKET_NAME: string;
    s3: AWS.S3;
    // Add your service methods here
    constructor() {
        const { ACCESS_KEY_ID, SECRET_ACCESS_KEY, AWS_S3_BUCKET_NAME } = configEnv();
        this.s3 = new AWS.S3({
            accessKeyId: ACCESS_KEY_ID,
            secretAccessKey: SECRET_ACCESS_KEY,
        });
    }

    async uploadSingle(file: Express.Multer.File) {
        const current = new Date();
        let temp: string[] = file?.originalname ? file.originalname.split('.') : [];
        let ext = temp.length > 1 ? `.${temp[temp.length - 1]}` : '';
        let LINK_UPLOAD_S3 = process.env.LINK_UPLOAD_S3;
        let fileName = `${current.getFullYear()}${current.getMonth() + 1}${current.getDate()}-${nanoid()}${ext}`;
        const key = `${LINK_UPLOAD_S3}/${fileName}`;
        const params = {
            Bucket: this.AWS_S3_BUCKET_NAME,
            Key: key, // File name you want to save as in S3
            Body: file.buffer,
            ACL: 'public-read',
            // ContentType: 'image/jpeg',
        };
        return new Promise<any>((resolve, reject) => {
            this.s3.upload(params, (err: any, data: any) => {
                if (err) {
                    reject(err);
                } else {
                    resolve({ fileName, fileUrl: data.Location });
                }
            });
        });
    }
}