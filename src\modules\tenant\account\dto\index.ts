import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSAccount } from '~/common/enums';

export class CreateAccountReq {
    @ApiProperty()
    @IsNotEmpty()
    username: string;

    @ApiProperty()
    @IsNotEmpty()
    password: string;

    @ApiProperty()
    @IsNotEmpty()
    fullName: string;

    @ApiPropertyOptional()
    avatar?: string;

    @ApiPropertyOptional({
        description: 'Type',
        enum: NSAccount.EType,
        default: NSAccount.EType.TENANT_USER,
    })
    type: NSAccount.EType;
}

export class ListAccountReq extends PageRequest {}

export class ChangePasswordByAccountReq {
    @ApiProperty()
    @IsNotEmpty()
    id: string;

    @ApiProperty()
    @IsNotEmpty()
    password: string;
}

export class LockAccountReq {
    @ApiProperty()
    @IsNotEmpty()
    id: string;
}

export class RegisterAppByAccountReq {
    @ApiProperty({
        description: 'Account ID',
        example: '123e4567-e89b-12d3-a456-************',
    })
    @IsNotEmpty()
    @IsUUID('4')
    accountId: string;

    @ApiProperty({
        description: 'Application codes',
        isArray: true,
        example: ['CRM', 'APE_CHAIN'],
    })
    appCodes: string[];
}

export class ListRegisteredAppByAccountReq {
    @ApiProperty()
    @IsNotEmpty()
    @IsUUID('4')
    accountId: string;
}
