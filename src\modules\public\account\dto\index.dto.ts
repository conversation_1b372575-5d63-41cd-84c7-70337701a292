import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { NSAccount } from '~/common/enums';

export class RegisterAccount {
    @ApiProperty({ description: 'Tenant ID (trường hợp tạo tài khoảng con)', required: true })
    @IsOptional()
    tenantId?: string;

    @ApiProperty({ description: 'Tên đăng nhập' })
    @IsNotEmpty()
    username?: string;

    @ApiProperty({ description: 'Mật khẩu' })
    @IsNotEmpty()
    password?: string;

    @ApiProperty({ description: 'Họ và tên' })
    @IsNotEmpty()
    fullName: string;

    @ApiProperty({ description: 'Loại tài khoản' })
    @IsNotEmpty()
    type: NSAccount.EType;

    @ApiProperty({ description: 'Ảnh đại diện', required: false })
    @IsOptional()
    avatar?: string;

    @ApiProperty({ description: 'Mã số thuế', required: false })
    @IsOptional()
    tax?: string;

    @ApiProperty({ description: 'Trạng thái', required: false })
    @IsOptional()
    status?: NSAccount.EStatus = NSAccount.EStatus.ACTIVE;

    @ApiProperty({ description: 'Địa chỉ', required: false })
    @IsOptional()
    address?: string;

    @ApiProperty({ description: 'Website', required: false })
    @IsOptional()
    website?: string;

    @ApiProperty({ description: 'Tên miền', required: false })
    @IsOptional()
    domain?: string;

    @ApiProperty({ description: 'Số điện thoại', required: false })
    @IsOptional()
    phone?: string;

    @ApiProperty({ description: 'Email', required: false })
    @IsOptional()
    email?: string;

    @ApiProperty({ description: 'Host của FE/BE', required: false })
    @IsOptional()
    host?: string;

    @ApiProperty({ description: 'Client ID (từ BE nào call qua)', required: false })
    @IsOptional()
    clientId?: string;
}
export class SyncDto {
    @ApiProperty({ description: 'Client ID' })
    @IsNotEmpty()
    clientId: string;

    @ApiProperty({ description: 'Danh sách tài khoản' })
    data: RegisterAccount[];
}

export class UpdateAccountDto {
    @ApiProperty({ description: 'Id của Account' })
    @IsNotEmpty()
    accountId: string;

    @ApiProperty({ description: 'Họ và tên' })
    @IsNotEmpty()
    fullName: string;

    @ApiProperty({ description: 'Ảnh đại diện', required: false })
    @IsOptional()
    avatar?: string;

    @ApiProperty({ description: 'Trạng thái', required: false })
    @IsOptional()
    status?: NSAccount.EStatus;
}

export class UpdateTenantDto {
    @ApiProperty({ description: 'Id của Tenant' })
    @IsNotEmpty()
    tenantId: string;

    @ApiPropertyOptional({ description: 'Tên Tenant' })
    @IsOptional()
    name?: string;

    @ApiPropertyOptional({ description: 'Tên miền', required: false })
    @IsOptional()
    domain?: string;

    @ApiPropertyOptional({ description: 'Trạng thái', required: false })
    @IsOptional()
    status?: NSAccount.EStatus;

    @ApiPropertyOptional({ description: 'Địa chỉ', required: false })
    @IsOptional()
    address?: string;

    @ApiPropertyOptional({ description: 'Website', required: false })
    @IsOptional()
    website?: string;

    @ApiPropertyOptional({ description: 'Số điện thoại', required: false })
    @IsOptional()
    phone?: string;

    @ApiPropertyOptional({ description: 'Email', required: false })
    @IsOptional()
    email?: string;

    @ApiPropertyOptional({ description: 'Mô tả', required: false })
    @IsOptional()
    description?: string;

    @ApiPropertyOptional({ description: 'Mã số thuế', required: false })
    @IsOptional()
    taxCode?: string;
}

export class UpdatePasswordDto {
    @ApiProperty({ description: 'Id của Account' })
    @IsNotEmpty()
    accountId: string;

    @ApiProperty({ description: 'Mật khẩu' })
    @IsNotEmpty()
    oldPassword: string;

    @ApiProperty({ description: 'Mật khẩu' })
    @IsNotEmpty()
    newPassword: string;

    @ApiProperty({ description: 'Xác nhận mật khẩu' })
    @IsNotEmpty()
    confirmPassword: string;
}
