import { Entity, Column } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSAccount } from '~/common/enums';

@Entity('user_admin')
export class UserAdminEntity extends PrimaryBaseEntity {
  @ApiProperty({ example: 'admin' })
  @Column({ nullable: true, unique: true, type: 'varchar', length: 255 })
  username: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'varchar', length: 255 })
  password?: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'text' })
  fullName?: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'varchar', length: 50 })
  type?: NSAccount.EAdminType;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'text' })
  avatar?: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, default: NSAccount.EStatus.ACTIVE })
  status?: NSAccount.EStatus;
}
