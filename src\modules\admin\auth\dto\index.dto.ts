import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";
import { NSAccount } from "~/common/enums";
import * as jwt from 'jsonwebtoken';
import { PageRequest } from "~/@systems/utils";

export class RegisterAdmin {
    @ApiProperty({ description: 'Tên đăng nhập' })
    @IsNotEmpty()
    username: string;

    @ApiProperty({ description: 'Mật khẩu' })
    @IsNotEmpty()
    password: string;

    @ApiProperty({ description: 'Họ và tên' })
    @IsNotEmpty()
    fullName: string;

    @ApiProperty({ description: 'Ảnh đại diện', required: false })
    @IsOptional()
    avatar?: string;

    @ApiProperty({ description: 'Loại tài khoản', required: false })
    @IsOptional()
    type?: NSAccount.EAdminType;

}

export class JwtPayload implements jwt.JwtPayload {
    @ApiPropertyOptional()
    iss?: string | undefined;
    @ApiPropertyOptional()
    sub?: string | undefined;
    @ApiPropertyOptional()
    aud?: string | string[] | undefined;
    @ApiPropertyOptional()
    exp?: number | undefined;
    @ApiPropertyOptional()
    nbf?: number | undefined;
    @ApiPropertyOptional()
    iat?: number | undefined;
    @ApiPropertyOptional()
    jti?: string | undefined;
}

export class LoginAdmin extends JwtPayload {
    @ApiProperty({ description: 'Tên đăng nhập' })
    @IsNotEmpty()
    username: string;

    @ApiProperty({ description: 'Mật khẩu' })
    @IsNotEmpty()
    password: string;
}

export class AdminSessionDto extends LoginAdmin {
    @ApiProperty()
    accessToken: string;
    @ApiProperty()
    refreshToken: string;
    @ApiProperty()
    tokenType: 'Bearer' = 'Bearer';
}

export class ListAdminDto extends PageRequest {
    
    @ApiPropertyOptional({description: "Username"})
    @IsOptional()
    username?: string

    @ApiPropertyOptional({description: "Status"})
    @IsOptional()
    status?: NSAccount.EStatus
}

export class UpdateInfoDto {
    @ApiProperty({description: "Id user admin"})
    @IsNotEmpty()
    id: string

    @IsOptional()
    fullname?: string

    @IsOptional()
    avatar?: string
}

export class ResetPasswordDto {
    @ApiProperty({description: "ID tài khoản"})
    id: string

    @ApiProperty({description: "Mật khẩu mới"})
    password: string

    @ApiProperty({description: "Xác nhận mật khẩu mới"})
    passwordConfirm: string
}