
# kubernetes-rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ape-ingress-manager-role
  namespace: ape-platform-dev
rules:
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses", "ingresses/status"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""] # "" là nhóm api cốt lõi (core)
  resources: ["services"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ape-ingress-manager-rolebinding
  namespace: ape-platform-dev
subjects:
- kind: ServiceAccount
  name: ape-ingress-applier
  namespace: ape-platform-dev
roleRef:
  kind: Role
  name: ape-ingress-manager-role
  apiGroup: rbac.authorization.k8s.io



  # ap-southeast-1:************:cluster/ape-eks

  # eksctl create iamserviceaccount \
  #   --name ape-ingress-applier \
  #   --namespace ape-platform-dev \
  #   --cluster ape-eks \
  #   --attach-policy-arn arn:aws:iam::************:policy/ape-eks-ingress \
  #   --approve \
  #   --profile ape-eks