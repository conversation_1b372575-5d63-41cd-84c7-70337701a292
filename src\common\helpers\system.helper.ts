import * as crypto from 'crypto';
import { generateCodeHelper } from './generate-code.helper';
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
dayjs.extend(isoWeek);

const SPERATOR_USERNAME = '@';
const SPERATOR_CLIENT_ID = '_';

const generateClientId = (applicationCode: string, tenantCode: string = '') => {
    if (tenantCode) {
        return `${applicationCode}${SPERATOR_CLIENT_ID}${tenantCode}${SPERATOR_CLIENT_ID}${generateCodeHelper.uuidNoDash().toLowerCase()}`;
    }
    return `${applicationCode}${SPERATOR_CLIENT_ID}${generateCodeHelper.uuidNoDash().toLowerCase()}`;
};

const generateClientSecret = () => {
    return crypto.randomBytes(32).toString('hex').toLowerCase().replace(/-/g, '_');
};

const generateUserNameAccount = (refixUserName: string, domain: string) => {
    return `${refixUserName}${SPERATOR_USERNAME}${domain}`;
};

const getTenantDomainByUserName = (userName: string) => {
    const tenantDomain = userName.split(SPERATOR_USERNAME)[1];
    return tenantDomain;
};

const generateAuthCode = () => {
    return crypto.randomBytes(16).toString('hex');
};

const generateQueryStringUrl = ({
    redirectUri,
    clientId,
    state,
}: {
    redirectUri: string;
    clientId: string;
    state?: string;
}) => {
    const obj = {
        client_id: clientId,
        redirect_uri: redirectUri,
        state: state || '',
    };
    const queryString = new URLSearchParams(obj).toString();
    return queryString;
};

const generateDomainByName = (name: string) => {
    const domain = name.split(' ')
    .map((w) => w[0])
    .join("")
    .toLowerCase();
    return domain;
}

export function getRange(period: 'week' | 'month' | 'year') {
    const now = dayjs();
    if (period === 'week') {
        // tuần hiện tại (thứ 2 -> chủ nhật theo ISO)
        return { start: now.startOf('week'), end: now.endOf('week'), bucketFmt: 'YYYY-MM-DD', granularity: 'day' };
    }
    if (period === 'month') {
        return { start: now.startOf('month'), end: now.endOf('month'), bucketFmt: 'YYYY-MM-DD', granularity: 'day' };
    }
    // year
    return { start: now.startOf('year'), end: now.endOf('year'), bucketFmt: 'YYYY-MM', granularity: 'month' };
}

export const systemHelper = {
    generateClientId,
    generateClientSecret,
    generateUserNameAccount,
    getTenantDomainByUserName,
    generateAuthCode,
    generateQueryStringUrl,
    generateDomainByName,
};
