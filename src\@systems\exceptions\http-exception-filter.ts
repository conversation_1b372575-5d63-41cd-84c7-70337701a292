import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { ValidateException, ApiException } from './dto';
import { EntityNotFoundError, QueryFailedError } from 'typeorm';
import { I18nService } from 'nestjs-i18n';

const separatorCharacter = ' ';

const pipeException = (
    dataPipe: { apiException: ApiException; type?: 'ValidateException' },
    i18n: I18nService,
) => {
    const { apiException, type = '' } = dataPipe;
    const { message: messageKey = '', errors: errorRoot = {} } = apiException;
    const message = messageKey.trim().replace(/\s/g, separatorCharacter);

    if (type && type === 'ValidateException') {
        const errors = {};
        Object.keys(errorRoot).forEach(key => {
            const value =
                errorRoot[key] && typeof errorRoot[key] === 'string'
                    ? errorRoot[key].trim().replace(/\s/g, separator<PERSON><PERSON>cter)
                    : errorRoot[key];

            Object.assign(errors, {
                [key]: value,
            });
        });
        return {
            ...apiException,
            errors,
            message: i18n.translate(message),
        };
    }
    return {
        ...apiException,
        message: i18n.translate(message),
    };
};

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
    constructor(private readonly i18n: I18nService) {}
    catch(exception: HttpException | ValidateException | any, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest<Request>();

        console.log('--------HttpExceptionFilter-----------');
        console.log(exception);
        console.log('-------------------');
        let status = 500;
        if (exception instanceof HttpException) {
            status = exception.getStatus();
            if (exception instanceof ValidateException) {
                response.status(status).send(
                    pipeException(
                        {
                            apiException: new ApiException(
                                'Value not right',
                                status,
                                exception.messages,
                            ),
                            type: 'ValidateException',
                        },
                        this.i18n,
                    ),
                );

                return;
            }
            console.log('Errorhandler', request.path, 'status', 'message:', exception.message);

            let res = exception.getResponse();
            if (res instanceof Object) {
                if (res['message']) {
                    if (typeof res['message'] === 'string') {
                        response.status(status).send(
                            pipeException(
                                {
                                    apiException: new ApiException(res['message'], status),
                                },
                                this.i18n,
                            ),
                        );
                        return;
                    } else {
                        response.status(status).send(
                            pipeException(
                                {
                                    apiException: new ApiException(
                                        'Unknown',
                                        status,
                                        res['message'],
                                    ),
                                },
                                this.i18n,
                            ),
                        );
                        return;
                    }
                } else {
                    response.status(status).send(
                        pipeException(
                            {
                                apiException: new ApiException('Unknown', status),
                            },
                            this.i18n,
                        ),
                    );
                    return;
                }
            }

            response.status(status).send(
                pipeException(
                    {
                        apiException: new ApiException('Unknown', status, res),
                    },
                    this.i18n,
                ),
            );
            return;
        }

        if (typeof exception === 'string') {
            response.status(HttpStatus.BAD_REQUEST).send(
                pipeException(
                    {
                        apiException: new ApiException(exception, status),
                    },
                    this.i18n,
                ),
            );
            return;
        }

        if (exception instanceof ApiException) {
            response.status(exception.httpCode).send(
                pipeException(
                    {
                        apiException: exception,
                    },
                    this.i18n,
                ),
            );
            return;
        }
        response.status(status).send(
            pipeException(
                {
                    apiException: new ApiException('Unknown', status, exception),
                },
                this.i18n,
            ),
        );
        return;
    }
}

// @Catch(QueryFailedError, EntityNotFoundError)
// export class TypeOrmFilter implements ExceptionFilter {
//     constructor(private readonly i18n: I18nService) {}
//     catch(exception: QueryFailedError | EntityNotFoundError, host: ArgumentsHost) {
//         const ctx = host.switchToHttp();
//         const response = ctx.getResponse<Response>();
//         response
//             .status(HttpStatus.BAD_REQUEST)
//             .send(
//                 new ApiException('Unknown', HttpStatus.BAD_REQUEST, { message: exception.message }),
//             );
//     }
// }
