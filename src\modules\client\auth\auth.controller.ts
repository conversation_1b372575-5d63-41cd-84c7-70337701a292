import { <PERSON>, Get, Post, Req, <PERSON>s, Body, Render, Query, UseGuards } from '@nestjs/common';
import { AuthService } from './auth.service';
import { Response } from 'express';
import { DefGet, DefPost } from 'nestjs-typeorm3-kit';

import { LoginReq, UserInfo } from './dto';
import { ClientAuthGuard } from '../@guards/client-auth/client-auth.guard';
import { I18nService } from 'nestjs-i18n';
import { ApiException } from '~/@systems/exceptions';
import { systemHelper } from '~/common/helpers';

@Controller('auth')
export class AuthController {
    constructor(
        private readonly authService: AuthService,
        private readonly i18n: I18nService,
    ) {}

    /**
     * Login single sign on direct by API
     */
    @DefPost('login')
    async login(@Body() body: LoginReq) {
        const code = await this.authService.validateLoginAndGenerateCode(
            body.username,
            body.password,
            body.clientId,
            body.redirectUri,
        );
        if (!code) {
            throw new ApiException('client_auth.login.error');
        }
        return this.authService.exchangeCodeForToken(
            code,
            body.clientId,
            body.clientSecret,
            body.redirectUri,
        );
    }

    /**
     * Render login page client single sign on
     */
    @Get('authorize')
    @Render('login')
    renderLogin(@Query() query: any) {
        const queryStringUrl = systemHelper.generateQueryStringUrl({
            redirectUri: query.redirect_uri,
            clientId: query.client_id,
            state: query.state,
        });
        return {
            redirectUri: query.redirect_uri,
            clientId: query.client_id,
            state: query.state,
            error: query.error,
            queryStringUrl,
        };
    }

    @DefPost('authorize')
    async doLogin(@Body() body: any, @Res() res: Response) {
        const { username, password, client_id: clientId, redirect_uri: redirectUri, state } = body;
        try {
            const code = await this.authService.validateLoginAndGenerateCode(
                username,
                password,
                clientId,
                redirectUri,
            );
            const url = new URL(redirectUri);
            url.searchParams.set('code', code);
            if (state) {
                url.searchParams.set('state', state);
            }
            return res.redirect(url.toString());
        } catch (error) {
            const queryStringUrl = systemHelper.generateQueryStringUrl({
                redirectUri,
                clientId,
                state,
            });
            return res.render('login', {
                redirectUri,
                clientId,
                state,
                error: this.i18n.t(error?.message || 'client_auth.login.error'),
                queryStringUrl,
            });
        }
    }

    @DefPost('token')
    async token(@Body() body: any) {
        const {
            code,
            client_id: clientId,
            client_secret: clientSecret,
            redirect_uri: redirectUri,
        } = body;
        return this.authService.exchangeCodeForToken(code, clientId, clientSecret, redirectUri);
    }

    @DefGet('userinfo', {
        responseType: UserInfo,
    })
    @UseGuards(ClientAuthGuard)
    userInfo(@Req() req) {
        const user = req.user;
        return this.authService.getUserInfo({
            id: user.sub,
        });
    }
}
