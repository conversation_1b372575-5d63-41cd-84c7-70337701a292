
import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule } from 'nestjs-typeorm3-kit';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { TenantService } from '../admin/tenant/tenant.service';

@ChildModule({
    imports: [PrimaryRepoModule],
    providers: [TenantService],
    exports: [TenantService],
})
export class ShareModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
