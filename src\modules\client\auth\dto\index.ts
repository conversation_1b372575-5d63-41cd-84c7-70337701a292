import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';
import { TenantEntity } from '~/domains/primary';

export class LoginReq {
    @ApiProperty()
    @IsNotEmpty()
    username: string;

    @ApiProperty()
    @IsNotEmpty()
    password: string;

    @ApiProperty()
    @IsNotEmpty()
    clientId: string;

    @ApiProperty()
    @IsNotEmpty()
    clientSecret: string;

    @ApiProperty()
    @IsNotEmpty()
    redirectUri: string;

    @ApiPropertyOptional()
    state: string;
}

export class UserInfo {
    @ApiProperty()
    id: string;

    @ApiProperty()
    username: string;

    @ApiPropertyOptional()
    fullName?: string;

    @ApiPropertyOptional()
    avatar?: string;

    @ApiProperty()
    tenantId: string;

    @ApiPropertyOptional()
    tenantInfo?: TenantEntity;
}
