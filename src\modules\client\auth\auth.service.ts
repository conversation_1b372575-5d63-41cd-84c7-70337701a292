import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import {
    AccountRepo,
    AuthorizationCodeRepo,
    TokenRepo,
    TenantApplicationRepo,
    ApplicationRepo,
    TenantRepo,
    AccountApplicationRepo,
} from '~/domains/primary';
import { NSAccount } from '~/common/enums';
import { JwtService } from '@nestjs/jwt';
import { systemHelper } from '~/common/helpers';
import { UserInfo } from './dto';
import { I18nService } from 'nestjs-i18n';
import securityHelper from '~/@core/helpers/security.helper';

// Constants for better maintainability
const TOKEN_CONSTANTS = {
    AUTH_CODE_EXPIRY_HOURS: 24,
    ACCESS_TOKEN_EXPIRY_SECONDS: 3600,
    DEFAULT_SCOPE: ['openid', 'profile'] as string[],
    TOKEN_TYPE: 'Bearer',
} as const;

interface ApplicationLookupResult {
    application: any;
    tenantId: string;
}

@Injectable()
export class AuthService {
    private readonly logger = new Logger(AuthService.name);

    constructor(
        private readonly jwtService: JwtService,
        @InjectRepo(TenantRepo)
        private readonly tenantRepo: TenantRepo,
        @InjectRepo(AccountRepo)
        private readonly accountRepo: AccountRepo,
        @InjectRepo(TenantApplicationRepo)
        private readonly tenantAppRepo: TenantApplicationRepo,
        @InjectRepo(AuthorizationCodeRepo)
        private readonly codeRepo: AuthorizationCodeRepo,
        @InjectRepo(TokenRepo)
        private readonly tokenRepo: TokenRepo,
        @InjectRepo(ApplicationRepo)
        private readonly applicationRepo: ApplicationRepo,
        @InjectRepo(AccountApplicationRepo)
        private readonly accountAppRepo: AccountApplicationRepo,
    ) {}

    /**
     * Helper method to find application and determine tenant ID
     * Reduces code duplication between methods
     */
    private async findApplicationAndTenant(
        clientId: string,
        clientSecret?: string,
        redirectUri?: string,
    ): Promise<ApplicationLookupResult> {
        // First try to find application directly
        let application = await this.applicationRepo.findOne({
            where: { clientId },
        });

        if (application) {
            // For direct applications, we need to get tenant from the request context
            // This assumes tenant is determined elsewhere in the flow
            return { application, tenantId: null };
        }

        // Try to find tenant application integration
        const tenantApp = await this.tenantAppRepo.findOne({
            where: {
                clientId,
                isActive: true,
                ...(clientSecret && { clientSecret }),
            },
        });

        if (!tenantApp) {
            this.logger.error(`Application not found for clientId: ${clientId}`);
            throw new UnauthorizedException(
                'auth_client.error.invalid_client_credentials_or_redirect_uri',
            );
        }

        // Validate redirect URI if provided
        if (redirectUri && !tenantApp.redirectUris?.includes(redirectUri)) {
            this.logger.error(`Invalid redirect URI: ${redirectUri} for clientId: ${clientId}`);
            throw new UnauthorizedException(
                'auth_client.error.invalid_client_credentials_or_redirect_uri',
            );
        }

        application = await this.applicationRepo.findOne({
            where: { id: tenantApp.applicationId },
        });

        if (!application) {
            this.logger.error(
                `Application not found for applicationId: ${tenantApp.applicationId}`,
            );
            throw new UnauthorizedException(
                'auth_client.error.invalid_client_credentials_or_redirect_uri',
            );
        }

        return { application, tenantId: tenantApp.tenantId };
    }

    /**
     * Helper method to validate tenant and get tenant ID
     */
    private async validateTenantFromUsername(username: string): Promise<string> {
        const tenantDomain = systemHelper.getTenantDomainByUserName(username);
        const tenant = await this.tenantRepo.findOne({
            where: { domain: tenantDomain },
        });

        if (!tenant) {
            this.logger.error(`Tenant not found for domain: ${tenantDomain}`);
            throw new UnauthorizedException('auth_client.error.tenant_not_found');
        }

        return tenant.id;
    }

    /**
     * Helper method to validate account permissions for application
     */
    private async validateAccountPermissions(account: any, application: any): Promise<void> {
        const allowAll =
            !application.allowedAccountTypes ||
            (Array.isArray(application.allowedAccountTypes) &&
                application.allowedAccountTypes.length === 0);

        if (!allowAll && !application.allowedAccountTypes.includes(account.type)) {
            this.logger.warn(
                `Account type ${account.type} not allowed for application ${application.id}`,
            );
            throw new UnauthorizedException('client_auth.account_type_not_allowed');
        }
    }

    /**
     * Helper method to create expiry date
     */
    private createExpiryDate(hours: number): Date {
        return new Date(Date.now() + hours * 60 * 60 * 1000);
    }

    async validateLoginAndGenerateCode(
        username: string,
        password: string,
        clientId: string,
        redirectUri: string,
    ): Promise<string> {
        try {
            // 1. Validate tenant from username
            const userTenantId = await this.validateTenantFromUsername(username);

            // 2. Find application and determine final tenant ID
            const { application, tenantId: appTenantId } = await this.findApplicationAndTenant(
                clientId,
                undefined,
                redirectUri,
            );

            // Use application tenant ID if available, otherwise use user tenant ID
            const finalTenantId = appTenantId || userTenantId;

            // 3. Validate redirect URI for direct applications
            if (!appTenantId && !application.redirectUris?.includes(redirectUri)) {
                this.logger.error(`Invalid redirect URI: ${redirectUri} for direct application`);
                throw new UnauthorizedException('auth_client.error.invalid_redirect_uri');
            }

            // 4. Find and validate account
            const account = await this.accountRepo.findOne({
                where: {
                    username,
                    tenantId: finalTenantId,
                    status: NSAccount.EStatus.ACTIVE,
                },
            });

            if (!account) {
                this.logger.warn(`Account not found: ${username} in tenant: ${finalTenantId}`);
                throw new UnauthorizedException('auth_client.error.account_not_active');
            }

            // 5. Validate account permissions for application
            await this.validateAccountPermissions(account, application);

            // 6. Verify password
            const isPasswordValid = await securityHelper.compare(password, account.password);
            if (!isPasswordValid) {
                this.logger.warn(`Invalid password for account: ${account.id}`);
                throw new UnauthorizedException('auth_client.error.invalid_password');
            }

            // check account is register application if account type is not tenant master
            if (account.type !== NSAccount.EType.TENANT_MASTER) {
                const accountApp = await this.accountAppRepo.findOne({
                    where: {
                        accountId: account.id,
                        applicationId: application.id,
                    },
                });
                if (!accountApp) {
                    this.logger.warn(`Account not register application: ${account.id}`);
                    throw new UnauthorizedException(
                        'auth_client.error.account_not_register_application',
                    );
                }
            }

            // 7. Generate and save authorization code
            const code = systemHelper.generateAuthCode();
            await this.codeRepo.save({
                accountId: account.id,
                clientId,
                code,
                expiresAt: this.createExpiryDate(TOKEN_CONSTANTS.AUTH_CODE_EXPIRY_HOURS),
                redirectUri,
                scope: TOKEN_CONSTANTS.DEFAULT_SCOPE,
            });

            this.logger.log(`Authorization code generated for account: ${account.id}`);
            return code;
        } catch (error) {
            this.logger.error(`Login validation failed: ${error.message}`, error.stack);
            throw error;
        }
    }

    async exchangeCodeForToken(
        code: string,
        clientId: string,
        clientSecret: string,
        redirectUri: string,
    ) {
        try {
            // 1. Find and validate application
            const { application } = await this.findApplicationAndTenant(
                clientId,
                clientSecret,
                redirectUri,
            );

            // 2. Validate and retrieve authorization code
            const codeEntity = await this.codeRepo.findOne({
                where: { code, clientId, redirectUri },
            });

            if (!codeEntity) {
                this.logger.warn(`Invalid authorization code: ${code} for clientId: ${clientId}`);
                throw new UnauthorizedException('Invalid code');
            }

            // 3. Check if code has expired
            if (codeEntity.expiresAt < new Date()) {
                this.logger.warn(`Expired authorization code: ${code}`);
                throw new UnauthorizedException('Authorization code has expired');
            }

            // 4. Verify account still exists and is active
            const account = await this.accountRepo.findOne({
                where: {
                    id: codeEntity.accountId,
                    status: NSAccount.EStatus.ACTIVE,
                },
            });

            if (!account) {
                this.logger.error(`Account not found or inactive: ${codeEntity.accountId}`);
                throw new UnauthorizedException('Account not found');
            }

            // 5. Generate tokens with different payloads for access and refresh
            const basePayload = {
                sub: codeEntity.accountId,
                clientId,
                tenantId: account.tenantId,
                username: account.username,
                applicationId: application.id,
                iat: Math.floor(Date.now() / 1000),
            };

            const accessTokenPayload = {
                ...basePayload,
                type: 'access',
            };

            const refreshTokenPayload = {
                ...basePayload,
                type: 'refresh',
            };

            const [accessToken, refreshToken] = await Promise.all([
                this.jwtService.signAsync(accessTokenPayload),
                this.jwtService.signAsync(refreshTokenPayload),
            ]);

            // 6. Save token information and clean up authorization code
            await Promise.all([
                this.tokenRepo.save({
                    accountId: codeEntity.accountId,
                    clientId,
                    accessToken,
                    refreshToken,
                    expiresAt: new Date(
                        Date.now() + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRY_SECONDS * 1000,
                    ),
                    scope: TOKEN_CONSTANTS.DEFAULT_SCOPE,
                }),
                // Clean up the used authorization code
                this.codeRepo.remove(codeEntity),
            ]);

            this.logger.log(`Tokens generated for account: ${account.id}, clientId: ${clientId}`);

            return {
                access_token: accessToken,
                refresh_token: refreshToken,
                expires_in: TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRY_SECONDS,
                token_type: TOKEN_CONSTANTS.TOKEN_TYPE,
            };
        } catch (error) {
            this.logger.error(`Token exchange failed: ${error.message}`, error.stack);
            throw error;
        }
    }

    async getUserInfo({ id }: { id: string }): Promise<UserInfo> {
        try {
            // Find account with active status check
            const account = await this.accountRepo.findOne({
                where: {
                    id,
                    status: NSAccount.EStatus.ACTIVE,
                },
            });

            if (!account) {
                this.logger.warn(`Account not found or inactive: ${id}`);
                throw new UnauthorizedException('auth_client.error.account_not_found');
            }
            const tenant = await this.tenantRepo.findOne({
                select: [
                    'id',
                    'name',
                    'domain',
                    'logoUrl',
                    'website',
                    'address',
                    'taxCode',
                    'phone',
                    'email',
                ],
                where: { id: account.tenantId },
            });

            const userInfo: UserInfo = {
                id: account.id,
                username: account.username,
                fullName: account.fullName || null,
                avatar: account.avatar || null,
                tenantId: account.tenantId,
                tenantInfo: tenant,
            };

            this.logger.log(`User info retrieved for account: ${id}`);
            return userInfo;
        } catch (error) {
            this.logger.error(`Failed to get user info: ${error.message}`, error.stack);
            throw error;
        }
    }
}
