import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils/page.utils';
import { NSTenant } from '~/common/enums';

export class CreateTenantAppReq {
    @ApiProperty()
    @IsNotEmpty()
    code: string;

    @ApiProperty()
    @IsOptional()
    redirectUris?: string[];

    @ApiPropertyOptional()
    tenantUrl?: string[];
}

export class RegisterTenantReq {
    @ApiProperty()
    @IsNotEmpty()
    name: string;

    @ApiProperty()
    @IsNotEmpty()
    domain: string;

    @ApiProperty()
    @IsNotEmpty()
    rootUsername: string;

    @ApiProperty()
    @IsNotEmpty()
    rootPassword: string;

    @ApiProperty()
    @IsNotEmpty()
    rootFullName: string;

    @ApiPropertyOptional()
    applications?: CreateTenantAppReq[];

    @ApiPropertyOptional()
    taxCode?: string;

    @ApiPropertyOptional()
    website?: string;

    @ApiPropertyOptional()
    address?: string;

    @ApiPropertyOptional()
    phone?: string;

    @ApiPropertyOptional()
    email?: string;

    @ApiPropertyOptional()
    tenantDomain?: string;
}

// list
export class ListTenantReq extends PageRequest {
    @ApiProperty({ description: 'Tên miền' })
    @IsOptional()
    domain: string;

    @ApiProperty({ description: 'Tên tenant' })
    @IsOptional()
    name: string;

    @ApiProperty({ description: 'Trạng thái' })
    status: NSTenant.EStatus;

    @ApiPropertyOptional({ example: '2021-01-01', description: 'Ngày tạo từ' })
    createdDateFrom?: string;

    @ApiPropertyOptional({ example: '2021-01-01', description: 'Ngày tạo đến' })
    createdDateTo?: string;

    @ApiPropertyOptional({ description: 'Tìm kiếm' })
    searchValue?: string;
}
// Update
export class UpdateTenantReq {
    @ApiProperty({ example: 'uuid', description: 'Mã tenant' })
    @IsNotEmpty()
    id: string;

    @ApiProperty()
    @IsNotEmpty()
    rootUsername: string;

    @ApiProperty()
    @IsNotEmpty()
    rootFullName: string;

    @ApiProperty()
    @IsNotEmpty()
    name: string;

    @ApiProperty()
    @IsNotEmpty()
    domain: string;

    @ApiPropertyOptional()
    address?: string;

    @ApiPropertyOptional()
    website?: string;

    @ApiPropertyOptional()
    phone?: string;

    @ApiPropertyOptional()
    email?: string;

    @ApiPropertyOptional()
    taxCode?: string;

    @ApiPropertyOptional()
    applications?: CreateTenantAppReq[];
}

export class UpdatePassword {
    @ApiProperty()
    @IsNotEmpty()
    tenantId: string;

    @ApiProperty()
    @IsNotEmpty()
    password: string;

    @ApiProperty()
    @IsNotEmpty()
    confirmPassword: string;
}

export class DetailsTenantReq {
    @ApiProperty()
    @IsNotEmpty()
    tenantId: string;
}

export class TenantAccountReq extends PageRequest {
    @ApiProperty({ description: 'Mã tenant' })
    @IsOptional()
    tenantId?: string;

    @ApiPropertyOptional({ example: '2021-01-01', description: 'Ngày tạo từ' })
    @IsOptional()
    createdDateFrom?: Date;

    @ApiPropertyOptional({ example: '2021-01-01', description: 'Ngày tạo đến' })
    @IsOptional()
    createdDateTo?: Date;
}


