import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsIn, IsOptional, IsUUID } from 'class-validator';

export class DashboardStatsDto {
    totalTenant: number;
    totalApplication: number;
    totalAccount: number;

    constructor(partial: Partial<DashboardStatsDto>) {
        Object.assign(this, partial);
    }
}


export class AnalyticsQueryDto {
    @ApiProperty({ enum: ['week', 'month', 'year'], default: 'week' })
    @IsIn(['week', 'month', 'year'])
    period: 'week' | 'month' | 'year' = 'week';

    @ApiPropertyOptional({ description: 'Múi giờ Postgres', default: 'Asia/Ho_Chi_Minh' })
    @IsOptional()
    tz?: string = 'Asia/Ho_Chi_Minh';

    @ApiPropertyOptional({ description: 'Lọc theo tenant', required: false })
    @IsOptional()
    @IsUUID()
    tenantId?: string;
}

export type PieItem = { type: string; count: number; percent: number };

export class OverviewAnalytics {
    @ApiProperty() totalUsers: number;
    @ApiProperty() newUsers: number; // trong period
    @ApiProperty() tenantMasters: number; // doanh nghiệp
    @ApiProperty() tenantUsers: number;   // nhân viên
    @ApiProperty({ type: [Object] }) pie: PieItem[];
}

export class GrowthPoint {
    @ApiProperty() bucket: string; // yyyy-MM-dd hoặc yyyy-MM
    @ApiProperty() count: number;
}

export class GrowthAnalytics {
    @ApiProperty({ type: [GrowthPoint] }) points: GrowthPoint[];
}

// dto/account-analytics.dto.ts
export class ChartJsLineData {
  labels: string[];
  datasets: { label: string; data: number[] }[];
}
