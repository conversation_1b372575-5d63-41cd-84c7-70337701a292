import { Type } from '@nestjs/common';
import { I18nService } from 'nestjs-i18n';
import { ApiException } from './dto';

const SEPARATOR = ' ';

export const ExceptionClassName = {
    ValidateException: 'ValidateException',
    HttpException: 'HttpException',
};
/**
 * Format a message by trimming and replacing all whitespace with a separator.
 */
export const formatMessage = (msg: string): string => {
    if (!msg) {
        return '';
    }
    return msg.trim().replace(/\s/g, SEPARATOR);
};

/**
 * Translate a message key using i18n after formatting.
 */
export const translateMessage = (i18n: I18nService, key: string): string => {
    return i18n.translate(formatMessage(key));
};

/**
 * Check if the exception matches a class by instanceof, constructor name or .name.
 */
export const checkTypeClass = (exception: any, typeClass: Type<any>, typeName: string): boolean => {
    return (
        exception instanceof typeClass ||
        exception?.constructor?.name === typeName ||
        exception?.name === typeName
    );
};

/**
 * Format and translate ApiException, optionally process validation errors.
 */
export const formatException = (
    exception: ApiException,
    i18n: I18nService,
    isValidation = false,
): ApiException & { errors?: Record<string, any> } => {
    const { message = '', errors = {} } = exception;
    const translatedMessage = translateMessage(i18n, message);

    if (isValidation) {
        const formattedErrors: Record<string, any> = {};
        for (const [key, value] of Object.entries(errors)) {
            formattedErrors[key] = typeof value === 'string' ? formatMessage(value) : value;
        }

        return {
            ...exception,
            message: translatedMessage,
            errors: formattedErrors,
        };
    }

    return {
        ...exception,
        message: translatedMessage,
    };
};
