import { existsSync, mkdirSync, writeFileSync } from 'fs';

export enum EAnsiColor {
    // Reset & styles
    Reset = '\x1b[0m', // Reset về mặc định (bỏ mọi style/màu trước đó)
    Bold = '\x1b[1m', // Chữ đậm
    Dim = '\x1b[2m', // Chữ mờ/nhạt
    Italic = '\x1b[3m', // Chữ nghiêng
    Underline = '\x1b[4m', // Gạch chân
    Blink = '\x1b[5m', // Nhấp nháy
    Reverse = '\x1b[7m', // Đ<PERSON>o màu (nền ↔ chữ)
    Hidden = '\x1b[8m', // Ẩn chữ (hiển thị trống)

    // Foreground - màu chữ cơ bản
    FgBlack = '\x1b[30m', // Đen
    FgRed = '\x1b[31m', // Đỏ
    FgGreen = '\x1b[32m', // Xanh lá
    <PERSON>ellow = '\x1b[33m', // Vàng
    FgBlue = '\x1b[34m', // Xanh dương
    FgMagenta = '\x1b[35m', // Tím Magenta
    FgCyan = '\x1b[36m', // Xanh cyan (xanh ngọc)
    FgWhite = '\x1b[37m', // Trắng

    // Background - màu nền cơ bản
    BgBlack = '\x1b[40m', // Nền đen
    BgRed = '\x1b[41m', // Nền đỏ
    BgGreen = '\x1b[42m', // Nền xanh lá
    BgYellow = '\x1b[43m', // Nền vàng
    BgBlue = '\x1b[44m', // Nền xanh dương
    BgMagenta = '\x1b[45m', // Nền tím Magenta
    BgCyan = '\x1b[46m', // Nền xanh cyan
    BgWhite = '\x1b[47m', // Nền trắng

    // Extended 256 colors (một số màu hay dùng)
    FgOrange = '\x1b[38;5;208m', // Cam (256-color)
    FgPink = '\x1b[38;5;200m', // Hồng
    FgLightBlue = '\x1b[38;5;81m', // Xanh biển nhạt
    FgGray = '\x1b[38;5;245m', // Xám trung tính
}

const colorReset = '\x1b[0m';
// ANSI escape codes cho 2 màu + reset
const infoColors = [EAnsiColor.FgGreen, EAnsiColor.FgYellow];

const info = (params: Array<string | number>, type: 'SHOW' | 'WRITE' = 'SHOW'): void => {
    const LOG_DIR = 'logs';
    const logFile = `system.log`;
    if (type === 'WRITE') {
        if (!existsSync(LOG_DIR)) {
            mkdirSync(LOG_DIR);
        }
        writeFileSync(`${LOG_DIR}/${logFile}`, params.join(' ') + '\n', { flag: 'a' });
        return;
    }
    const output = params
        .map((text, idx) => {
            const color = infoColors[idx % infoColors.length];
            return `${color}${String(text)}${colorReset}`;
        })
        .join(' ');
    console.log(output);
};

export const loggerHelper = {
    info,
};
