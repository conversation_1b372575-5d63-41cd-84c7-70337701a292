# Client Module

Client module implements OAuth 2.0 Authorization Code flow for client applications to authenticate users and access protected resources.

## Overview

This module provides endpoints for client applications to:
- Authenticate users via direct API or web interface
- Generate authorization codes
- Exchange codes for access tokens
- Retrieve user information

## Endpoints

### POST /client/auth/login

**Direct API Login**

Authenticates a user directly via API and returns access tokens immediately.

**Request Body:**
```json
{
  "username": "string",
  "password": "string", 
  "clientId": "string",
  "clientSecret": "string",
  "redirectUri": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "token_type": "Bearer",
  "expires_in": "number"
}
```

**Use Case:** For mobile apps or SPAs that need direct authentication without browser redirects.

---

### GET /client/auth/authorize

**Authorization Page**

Renders the login page for OAuth 2.0 authorization code flow.

**Query Parameters:**
- `client_id` - Client application identifier
- `redirect_uri` - URI to redirect after authorization
- `state` - Optional state parameter for CSRF protection
- `error` - Optional error message to display

**Response:** HTML login page

**Use Case:** Standard OAuth 2.0 web flow where users are redirected to authenticate.

---

### POST /client/auth/authorize

**Process Authorization**

Processes the login form submission and redirects with authorization code.

**Request Body:**
```json
{
  "username": "string",
  "password": "string",
  "client_id": "string", 
  "redirect_uri": "string",
  "state": "string (optional)"
}
```

**Success Response:** HTTP 302 redirect to `redirect_uri` with:
- `code` - Authorization code
- `state` - Original state parameter (if provided)

**Error Response:** Re-renders login page with error message

**Use Case:** Completes the authorization step in OAuth 2.0 flow.

---

### POST /client/auth/token

**Token Exchange**

Exchanges authorization code for access and refresh tokens.

**Request Body:**
```json
{
  "code": "string",
  "client_id": "string",
  "client_secret": "string", 
  "redirect_uri": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "refresh_token": "string",
  "token_type": "Bearer", 
  "expires_in": "number"
}
```

**Use Case:** Final step in OAuth 2.0 flow to obtain tokens for API access.

---

### GET /client/auth/userinfo

**User Information**

Retrieves authenticated user's profile information.

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "id": "string",
  "username": "string",
  "email": "string",
  "tenantInfo": {
    "id": "string",
    "name": "string"
  }
}
```

**Use Case:** Get user profile data for authenticated sessions.

## Security Features

- **Authorization Code Expiry**: Codes expire after use or timeout
- **Client Validation**: Validates client credentials and redirect URIs
- **Tenant Isolation**: Supports multi-tenant authentication
- **CSRF Protection**: State parameter validation
- **Token Security**: Separate access and refresh token payloads

## Authentication Flow

1. **Authorization Request**: Client redirects user to `/authorize` endpoint
2. **User Authentication**: User enters credentials on login page
3. **Authorization Grant**: System generates authorization code and redirects back
4. **Token Request**: Client exchanges code for tokens at `/token` endpoint
5. **Resource Access**: Client uses access token to call `/userinfo` and other APIs

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:
- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (invalid credentials)
- `403` - Forbidden (insufficient permissions)
- `500` - Internal Server Error

Error responses include localized messages via the i18n service.
