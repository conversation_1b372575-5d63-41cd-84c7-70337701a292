import { Body, UseGuards } from '@nestjs/common';
import { DefController, DefPost } from 'nestjs-typeorm3-kit';
import { TenantAccountGuard } from '../@guards/tenant-account/tenant-account.guard';
import { ApplicationService } from './application.service';
import {
    ListAccountRegisteredByApplicationReq,
    ListApplicationReq,
    ListRegisteredApplicationReq,
    RegisterApplicationReq,
    UnregisterApplicationReq,
} from './dto';

@UseGuards(TenantAccountGuard)
@DefController('applications')
export class ApplicationController {
    constructor(private readonly applicationService: ApplicationService) {}

    @DefPost('register')
    async register(@Body() body: RegisterApplicationReq) {
        return this.applicationService.register(body);
    }

    @DefPost('unregister')
    async unregister(@Body() body: UnregisterApplicationReq) {
        return this.applicationService.unregister(body);
    }

    @DefPost('list')
    async list(@Body() body: ListApplicationReq) {
        return this.applicationService.list(body);
    }

    @DefPost('list-registered')
    async listRegistered(@Body() body: ListRegisteredApplicationReq) {
        return this.applicationService.listRegistered(body);
    }

    @DefPost('list-account-registered-by-application')
    async listAccountRegisteredByApplication(@Body() body: ListAccountRegisteredByApplicationReq) {
        return this.applicationService.listAccountRegisteredByApplication(body);
    }
}
