import { Entity, Column } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('authorization_code')
export class AuthorizationCodeEntity extends PrimaryBaseEntity {
  @Column({ type: 'uuid' })
  accountId: string;

  @Column({ length: 255 })
  clientId: string;

  @Column({ length: 255 })
  code: string;

  @Column({ type: 'timestamp' })
  expiresAt: Date;

  @Column({ length: 1024, nullable: true })
  redirectUri?: string;

  @Column({ type: 'text', array: true, default: '{}' })
  scope: string[];
}
