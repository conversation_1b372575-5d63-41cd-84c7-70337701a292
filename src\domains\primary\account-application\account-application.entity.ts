import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Entity, Column } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('account_application')
export class AccountApplicationEntity extends PrimaryBaseEntity {
    @ApiProperty()
    @Column({ type: 'uuid' })
    accountId: string;

    @ApiProperty()
    @Column({ type: 'uuid' })
    tenantId: string;

    @ApiProperty()
    @Column({ type: 'uuid' })
    applicationId: string;
}
