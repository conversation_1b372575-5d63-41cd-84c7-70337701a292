export namespace NSAccount {
    export enum EStatus {
        INACTIVE = 'INACTIVE',
        ACTIVE = 'ACTIVE',
        LOCKED = 'LOCKED',
    }
    export enum EType {
        TENANT_MASTER = 'TENANT_MASTER', // root
        TENANT_USER = 'TENANT_USER', // user con của tenant
        CUSTOMER = 'CUSTOMER', // khách hàng của tenant
    }

    export const ETypeValue = {
        [EType.TENANT_MASTER]: {
            label: 'Root',
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
        },
        [EType.TENANT_USER]: {
            label: 'User con',
            borderColor: 'rgba(54, 162, 235, 1)',
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
        },
        [EType.CUSTOMER]: {
            label: 'Khách hàng',
            borderColor: 'rgba(255, 206, 86, 1)',
            backgroundColor: 'rgba(255, 206, 86, 0.2)',
        },
    };

    export enum EAdminType {
        SUPER_ADMIN = 'SUPER_ADMIN',
        ADMIN = 'ADMIN',
    }
}
