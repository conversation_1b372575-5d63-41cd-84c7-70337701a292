import { ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';

export const GUARD_CODE = 'tenant-account-jwt';

@Injectable()
export class TenantAccountGuard extends AuthGuard(GUARD_CODE) {
    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
        // const req = context.switchToHttp().getRequest();
        // const token = req.headers.authorization?.split(' ')[1];
        // if (!token) {
        //     throw new UnauthorizedException();
        // }
        return super.canActivate(context);
    }
}
