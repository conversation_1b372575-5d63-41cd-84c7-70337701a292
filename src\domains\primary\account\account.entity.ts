import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Entity, Column } from 'typeorm';
import { NSAccount } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('account')
export class AccountEntity extends PrimaryBaseEntity {
    @ApiProperty()
    @Column({ type: 'uuid' })
    tenantId: string;

    @ApiProperty()
    @Column({ type: 'varchar', unique: true, length: 255 })
    username: string;

    @ApiProperty()
    @Column({ type: 'varchar', length: 255 })
    password: string;

    @ApiPropertyOptional()
    @Column({ type: 'varchar', length: 20, default: NSAccount.EType.TENANT_MASTER })
    type: NSAccount.EType;

    @ApiPropertyOptional()
    @Column({ length: 255, nullable: true })
    fullName?: string;

    @ApiPropertyOptional()
    @Column({ length: 1024, nullable: true })
    avatar?: string;

    @ApiPropertyOptional()
    @Column({ default: NSAccount.EStatus.INACTIVE })
    status: NSAccount.EStatus;
}
