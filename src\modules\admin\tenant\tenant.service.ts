import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import dayjs from 'dayjs';
import { isEqual } from 'lodash';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import { Between, ILike, In } from 'typeorm';
import securityHelper from '~/@core/helpers/security.helper';
import { BusinessException } from '~/@systems/exceptions';
import { NSAccount, NSTenant } from '~/common/enums';
import { systemHelper } from '~/common/helpers';
import {
    AccountRepo,
    ApplicationRepo,
    TenantApplicationRepo,
    TenantRepo,
    TokenRepo,
} from '~/domains/primary';
import {
    ListTenantReq,
    RegisterTenantReq,
    TenantAccountReq,
    UpdatePassword,
    UpdateTenantReq,
} from './dto';

@Injectable()
export class TenantService {
    constructor(
        private jwtService: JwtService,
        @InjectRepo(TenantRepo)
        readonly tenantRepo: TenantRepo,
        @InjectRepo(AccountRepo)
        readonly accountRepo: AccountRepo,
        @InjectRepo(ApplicationRepo)
        readonly applicationRepo: ApplicationRepo,
        @InjectRepo(TenantApplicationRepo)
        readonly tenantApplicationRepo: TenantApplicationRepo,
        @InjectRepo(TokenRepo)
        readonly tokenRepo: TokenRepo,
    ) {}
    async list(body: ListTenantReq) {
        const {
            createdDateFrom,
            createdDateTo,
            pageIndex,
            pageSize,
            searchValue,
            domain,
            name,
            status,
        } = body;
        const baseFilter: any = {};

        if (status) {
            baseFilter.status = status;
        }

        if (createdDateFrom && createdDateTo) {
            baseFilter.createdDate = Between(new Date(createdDateFrom), new Date(createdDateTo));
        }

        let where: any;

        if (searchValue) {
            where = [
                { ...baseFilter, name: ILike(`%${searchValue}%`) },
                { ...baseFilter, domain: ILike(`%${searchValue}%`) },
            ];
        } else {
            if (name) baseFilter.name = ILike(`%${name}%`);
            if (domain) baseFilter.domain = ILike(`%${domain}%`);
            where = baseFilter;
        }

        return this.tenantRepo.findPagination(
            { where, order: { createdDate: 'DESC' } },
            { pageIndex, pageSize },
        );
    }

    async details(id: string) {
        const data = await this.tenantRepo.findOne({ where: { id } });
        if (!data) {
            throw new BusinessException('tenant.detail.error.tenant_not_found');
        }
        const applications = await this.tenantApplicationRepo.find({
            where: { tenantId: data.id },
        });
        const applicationLst = await this.applicationRepo.find();
        const mapApplications = applications.map(item => {
            const app = applicationLst.find(app => app.id === item.applicationId);
            console.log(app);
            return {
                ...item,
                clientId: app.clientId,
                clientSecret: app.clientSecret,
                redirectUris: app.redirectUris,
                status: app.status,
                code: app.code,
                name: app.name,
                description: app.description,
                linkLogins: item.redirectUris?.map(
                    uri =>
                        `${data.domain}/api/client/auth/authorize?client_id=${item.clientId}&redirect_uri=${uri}`,
                ),
            };
        });
        const account = await this.accountRepo.findOne({ where: { tenantId: data.id } });
        delete account.password;

        return {
            ...data,
            applications: mapApplications,
            rootAccount: account,
            rootUsername: account?.username,
            rootFullName: account?.fullName,
            rootAvatar: account?.avatar,
        };
    }

    @DefTransaction()
    async register(body: RegisterTenantReq) {
        // 1. Kiểm tra domain đã tồn tại chưa
        const checkTenant = await this.tenantRepo.findOne({ where: { domain: body.domain } });
        if (checkTenant) {
            throw new BusinessException('tenant.register.error.tenant_existed');
        }

        const userName = systemHelper.generateUserNameAccount(body.rootUsername, body.domain);
        const password = await securityHelper.hash(body.rootPassword);
        // 1. Check nếu đã tồn tại tenant trên hệ thống thì chỉ tạo account Root
        // 2. Không tạo Tenant nữa
        // ! Tennat được sinh ra từ api sync suppliers từ hệ thống khác
        const tenantTaxCode = await this.tenantRepo.findOne({ where: { taxCode: body.taxCode } });
        if (tenantTaxCode) {
            const account = await this.accountRepo.save({
                tenantId: tenantTaxCode.id,
                username: body.rootUsername,
                password,
                fullName: body.rootFullName,
                type: NSAccount.EType.TENANT_MASTER, // root
                status: NSAccount.EStatus.ACTIVE,
            });
            delete account.password;
            return {
                tenant: tenantTaxCode,
                account,
            };
        }

        // 2. Tạo mới tenant
        const tenant = await this.tenantRepo.save({
            name: body.name,
            domain: body.domain,
            taxCode: body.taxCode,
            website: body.website,
            address: body.address,
            phone: body.phone,
            email: body.email,
            status: NSTenant.EStatus.ACTIVE,
        });

        // 3. Tạo account root
        const account = await this.accountRepo.save({
            tenantId: tenant.id,
            username: userName,
            password,
            fullName: body.rootFullName,
            type: NSAccount.EType.TENANT_MASTER,
            status: NSAccount.EStatus.ACTIVE,
        });
        delete account.password;

        // 4. Mapping applications
        // body.applications: [{ code, redirectUris }]
        const appResults = [];
        if (body.applications && Array.isArray(body.applications)) {
            for (const appReq of body.applications) {
                // Lấy hoặc tạo app gốc
                let app = await this.applicationRepo.findOne({ where: { code: appReq.code } });
                if (!app) {
                    throw new BusinessException('Application not found');
                }

                // Mapping vào tenant_application
                await this.tenantApplicationRepo.save({
                    tenantId: tenant.id,
                    applicationId: app.id,
                    redirectUris: appReq.redirectUris,
                    isActive: true,
                    plan: 'default',
                    tenantDomain: appReq.tenantUrl,
                });
                appResults.push({
                    appCode: app.code,
                    appName: app.name,
                    redirectUris: appReq.redirectUris,
                    clientId: systemHelper.generateClientId(app.code),
                    clientSecret: systemHelper.generateClientSecret(),
                });
            }
        }

        // 5. Trả về kết quả
        return {
            tenant,
            account,
            applications: appResults,
        };
    }

    @DefTransaction()
    async update(body: UpdateTenantReq) {
        try {
            const { applications, rootUsername, rootFullName, id, ...rest } = body;

            const tenant = await this.tenantRepo.findOne({ where: { id } });
            if (!tenant) {
                throw new BusinessException('Tenant not found');
            }

            // 1. Cập nhật danh sách application nếu có
            if (applications && applications.length > 0) {
                const existingMappings = await this.tenantApplicationRepo.find({
                    where: { tenantId: tenant.id },
                });
                const allApplications = await this.applicationRepo.find();

                for (const app of applications) {
                    let appEntity = allApplications.find(a => a.code === app.code);

                    // Nếu app chưa có trong bảng application → tạo mới
                    if (!appEntity) {
                        appEntity = await this.applicationRepo.save({
                            code: app.code,
                            name: app.code,
                        });
                    }

                    // Kiểm tra tenant đã gán app này chưa
                    const existingMapping = existingMappings.find(
                        m => m.applicationId === appEntity.id,
                    );

                    if (existingMapping) {
                        // So sánh redirectUris, nếu khác thì update
                        const isSameUris = isEqual(
                            [...(existingMapping.redirectUris || [])].sort(),
                            [...(app.redirectUris || [])].sort(),
                        );

                        if (!isSameUris) {
                            await this.tenantApplicationRepo.update(
                                { id: existingMapping.id },
                                {
                                    redirectUris: app.redirectUris,
                                },
                            );
                        } else {
                            await this.tenantApplicationRepo.update(
                                { id: existingMapping.id },
                                {
                                    tenantDomain: app.tenantUrl,
                                },
                            );
                        }
                    } else {
                        // Nếu chưa mapping → tạo mới với clientId/clientSecret
                        const clientId = systemHelper.generateClientId(app.code, tenant.domain);
                        const clientSecret = systemHelper.generateClientSecret();

                        await this.tenantApplicationRepo.save({
                            tenantId: tenant.id,
                            applicationId: appEntity.id,
                            clientId,
                            clientSecret,
                            redirectUris: app.redirectUris,
                            isActive: true,
                            plan: 'default',
                            tenantDomain: app.tenantUrl,
                        });
                    }
                }
            }

            // 2. Cập nhật root account nếu có
            if (rootUsername || rootFullName) {
                const rootAccount = await this.accountRepo.findOne({
                    where: { tenantId: tenant.id, type: NSAccount.EType.TENANT_MASTER },
                });
                if (!rootAccount) {
                    throw new BusinessException('Root account not found');
                }
                if (rootUsername)
                    rootAccount.username = systemHelper.generateUserNameAccount(
                        rootUsername,
                        tenant.domain,
                    );
                if (rootFullName) rootAccount.fullName = rootFullName;

                await this.accountRepo.save(rootAccount);
            }

            // 3. Cập nhật thông tin tenant
            await this.tenantRepo.update(tenant.id, rest);

            return { success: true };
        } catch (error) {
            console.error(error);
            throw new BusinessException(error.message);
        }
    }

    @DefTransaction()
    async resetPassword(body: UpdatePassword) {
        const tenant = await this.tenantRepo.findOne({ where: { id: body.tenantId } });
        if (!tenant) {
            throw new BusinessException('Tenant not found');
        }
        const rootAccount = await this.accountRepo.findOne({
            where: { tenantId: tenant.id, type: NSAccount.EType.TENANT_MASTER },
        });
        if (!rootAccount) {
            throw new BusinessException('Root account not found');
        }
        if (body.password !== body.confirmPassword) {
            throw new BusinessException('Password not match');
        }
        rootAccount.password = await securityHelper.hash(body.password);
        await this.accountRepo.save(rootAccount);
        return rootAccount;
    }

    async listApplications(domain: string) {
        const sql = `
        SELECT code, name,"clientId","clientSecret","redirectUris"
        FROM application  
        order by "createdDate" desc
        `;
        const data = await this.tenantApplicationRepo.query<
            {
                code: string;
                name: string;
                clientId: string;
                clientSecret: string;
                redirectUris: string[];
            }[]
        >(sql);

        return data.map(item => ({
            ...item,
            linkLogins: item.redirectUris.map(
                uri =>
                    `${domain}/api/client/auth/authorize?client_id=${item.clientId}&redirect_uri=${uri}`,
            ),
        }));
    }

    @DefTransaction()
    async inactive(id: string) {
        const tenant = await this.tenantRepo.findOne({ where: { id } });
        if (!tenant) {
            throw new BusinessException('Tenant not found');
        }
        tenant.status = NSTenant.EStatus.INACTIVE;
        return this.tenantRepo.save(tenant);
    }

    @DefTransaction()
    async active(id: string) {
        const tenant = await this.tenantRepo.findOne({ where: { id } });
        if (!tenant) {
            throw new BusinessException('Tenant not found');
        }
        tenant.status = NSTenant.EStatus.ACTIVE;
        return this.tenantRepo.save(tenant);
    }
    async listAccountActivity(body: TenantAccountReq) {
        const { tenantId, createdDateFrom, createdDateTo, pageIndex, pageSize } = body;
        let { data, total } = await this.accountRepo.findPagination(
            {
                where: {
                    ...(tenantId && { tenantId }),
                },
            },
            { pageIndex, pageSize },
        );

        const listAccountId = data.map(item => item.id);
        const dictAccount = {};
        data.forEach(item => {
            dictAccount[item.id] = item;
        });
        //find distinct idtoken
        const listToken = await this.tokenRepo.find({
            where: {
                ...(createdDateFrom &&
                    createdDateTo && {
                        createdDate: Between(createdDateFrom, createdDateTo),
                    }),
                accountId: In(listAccountId),
            },
            order: {
                createdDate: 'DESC',
            },
            select: ['id', 'accountId', 'createdDate'],
        });

        //map thông tin account
        data.forEach(async (item: any) => {
            item.numberLogin = listToken.filter(token => token.accountId == item.id).length;
            //lầm đăng nhập gần nhất
            item.lastLogin = listToken.find(token => token.accountId == item.id)?.createdDate;
        });
        return { data: data, total };
    }

    // async getInformation(body: TenantAccountReq) {
    //     //format promiseall

    //     const tenantAccount = this.accountRepo.findPagination(
    //         {
    //             where: {
    //                 ...(body.tenantId && { tenantId: body.tenantId }),
    //                 ...(body.createdDateFrom &&
    //                     body.createdDateTo && {
    //                         createdDate: Between(body.createdDateFrom, body.createdDateTo),
    //                     }),
    //             },
    //         },
    //         { pageIndex: body?.pageIndex, pageSize: body?.pageSize },
    //     );

    //     const listAllAccount = this.accountRepo.find({
    //         where: {
    //             ...(body.tenantId && { tenantId: body.tenantId }),
    //             ...(body.createdDateFrom &&
    //                 body.createdDateTo && {
    //                     createdDate: Between(body.createdDateFrom, body.createdDateTo),
    //                 }),
    //         },

    //         select: ['id', 'status'],
    //     });

    //     const [{ data, total }, listActiveAccount] = await Promise.all([
    //         tenantAccount,
    //         listAllAccount,
    //     ]);

    //     const activeAccount = listActiveAccount.filter(
    //         item => item.status == NSAccount.EStatus.ACTIVE,
    //     ).length;

    //     const inActiveAccount = listActiveAccount.filter(
    //         item => item.status == NSAccount.EStatus.INACTIVE,
    //     ).length;
    //     //delete password key
    //     data.forEach(item => {
    //         delete item.password;
    //     });
    //     let userActivitiesStats = {};
    //     if (body.createdDateFrom && body.createdDateTo) {
    //         userActivitiesStats = await this.getUserActivitiesStats(body);
    //     }

    //     return {
    //         userActivitiesStats,
    //         activeAccount: activeAccount,
    //         inActiveAccount: inActiveAccount,
    //         totalAccount: total,
    //         tenantInfos: data,
    //     };
    // }

    async getUserActivitiesStats(body: TenantAccountReq) {
        const { tenantId, createdDateFrom, createdDateTo } = body;
        const listAccount = await this.accountRepo.find({
            where: {
                ...(tenantId && { tenantId }),
            },
        });
        const listAccountId = listAccount.map(item => item.id);
        const dictAccount = {};
        listAccount.forEach(item => {
            dictAccount[item.id] = item;
        });
        //find distinct idtoken
        const listActivity = await this.tokenRepo.find({
            where: {
                ...(createdDateFrom &&
                    createdDateTo && {
                        createdDate: Between(createdDateFrom, createdDateTo),
                    }),
                accountId: In(listAccountId),
            },
            select: ['accountId', 'createdDate'],
        });

        //list account id
        const totalActivity = [...new Set(listActivity.map(item => item.accountId))].length;
        const totalNonActivity = listAccount.length - totalActivity;
        //create a Dict of everyday in the month
        let dateRange = dayjs(createdDateTo).diff(createdDateFrom, 'day') + 1;
        if (dateRange < 12) {
            dateRange = 12;
        }
        //lấy khoảng cách giữa các ngày sau khi chia cho 12 để căn chỉnh cho chart
        const step = Math.ceil(dateRange / 12);
        const label = [];

        for (let i = 1; i <= dateRange; i++) {
            label.push(
                dayjs(createdDateFrom)
                    .add(i - 1, 'day')
                    .format('DD/MM'),
            );
        }

        const dictAccountType = {};
        let datasets = [];
        listAccount.forEach(item => {
            if (!dictAccountType[item.type]) {
                dictAccountType[item.type] = true;
                let newData = {
                    label: NSAccount.ETypeValue[item.type].label,
                    data: [],
                    backgroundColor: NSAccount.ETypeValue[item.type].backgroundColor,
                    borderColor: NSAccount.ETypeValue[item.type].borderColor,
                };

                for (let i = 1; i <= dateRange; i++) {
                    newData.data[i - 1] = 0;
                }
                datasets.push(newData);
            }
        });

        listActivity.forEach(item => {
            const account = dictAccount[item.accountId];
            //get index of date fromdate todate dayjs diff
            const index = dayjs(item.createdDate).diff(createdDateFrom, 'day');

            datasets.forEach(data => {
                if (data.label == NSAccount.ETypeValue[account.type].label) {
                    data.data[index] += 1;
                }
            });
        });

        return {
            totalAccount: listAccount.length,
            totalActivity,
            totalNonActivity,
            step,
            label,
            datasets,
        };
    }
}
