# APE SSO

## Giải thích module chính
### Ad<PERSON> cho web admin, user ape sẽ quản lý Application, Tenant, Dashboard, Bills, v.v..
### Client
Dành cho phần xác thực SSO từ hệ thống khác
### Public
Public 1 số API để sync data
### Tenant
Dành cho web tenant, tenant sẽ quản lý user, đăng ký application với ape, v.v..

### INIT

```sh
    cp  .env.example .env
```

### Update ENV PROP

change information in .env file

### INSTALL

```sh
    yarn install
```

### START

```sh
    yarn dev
```

### DEBUG

```sh
    yarn debug
```

### RUN MAC CHIP M

```sh
    yarn
    cd node_modules/bcrypt && npm rebuild bcrypt --build-from-source
    yarn dev
```
