import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { AccountService } from './account.service';
import {
    RegisterAccount,
    SyncDto,
    UpdateAccountDto,
    UpdateTenantDto,
    UpdatePasswordDto,
} from './dto/index.dto';
import { Body } from '@nestjs/common';
import { ResetPasswordAccountDto } from '~/modules/admin/account/dto/account.dto';

@DefController('account')
export class AccountController {
    constructor(private readonly accountService: AccountService) {}

    @DefPost('register', {
        summary: 'Register a new account',
    })
    registerAccount(@Body() body: RegisterAccount) {
        return this.accountService.registerAccount(body);
    }

    @DefPost('sync-suppliers', {
        summary: 'Sync suppliers',
    })
    syncSuppliers(@Body() body: SyncDto) {
        const { clientId, data } = body;
        return this.accountService.syncSuppliers(clientId, data);
    }

    @DefPost('sync-update-account', {
        summary: 'Sync update account',
    })
    syncUpdateAccount(@Body() body: UpdateAccountDto) {
        return this.accountService.updateAccount(body);
    }

    @DefPost('sync-update-tenant', {
        summary: 'Sync update tenant',
    })
    syncUpdateTenant(@Body() body: UpdateTenantDto) {
        return this.accountService.updateTenant(body);
    }

    @DefPost('sync-update-password', {
        summary: 'Sync update password',
    })
    syncUpdatePassword(@Body() body: UpdatePasswordDto) {
        return this.accountService.updatePassword(body);
    }

    // Reset password for root
    @DefPost('sync-reset-password', {
        summary: 'Root reset password f r user, customer',
    })
    resetPassword(@Body() body: ResetPasswordAccountDto) {
        return this.accountService.resetPassword(body);
    }
     

    @DefGet('get-account', {
        summary: 'Get account',
    })
    getAccount() {
        return this.accountService.getAccountsWithPMS();
    }
}
