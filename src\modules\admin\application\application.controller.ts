
// Application Controller
import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { ApplicationService } from './application.service';
import { Body, Query } from '@nestjs/common';
import { CreateApplicationDto, UpdateApplicationDto, ApplicationListDto } from './dto/application.dto';
import { Roles } from '~/@core/decorator/role.decorator';
import { NSAccount } from '~/common/enums';

@DefController('applications')
@Roles(NSAccount.EAdminType.SUPER_ADMIN)
export class ApplicationController {
    constructor(private readonly adminApplicationService: ApplicationService) {}
    @DefPost('create', {
        summary: 'Create application',
    })
    create(@Body() body: CreateApplicationDto) {
        return this.adminApplicationService.create(body);
    }

    @DefPost('update', {
        summary: 'Update application',
    })
    update(@Body() body: UpdateApplicationDto) {
        return this.adminApplicationService.update(body);
    }

    @DefGet('list', {
        summary: 'List applications',
    })
    list(@Query() params: ApplicationListDto) {
        return this.adminApplicationService.list(params);
    }

    // inactive / active
    @DefPost('inactive', {
        summary: 'Inactive application',
    })
    inactive(@Body() body: { id: string; status: string }) {
        return this.adminApplicationService.inactive(body.id);
    }

    @DefPost('active', {
        summary: 'Active application',
    })
    active(@Body() body: { id: string }) {
        return this.adminApplicationService.active(body.id);
    }
}


