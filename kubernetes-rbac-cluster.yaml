
# kubernetes-rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: ape-ingress-manager-role
rules:
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses", "ingresses/status"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: [""] # "" là nhóm api cốt lõi (core)
  resources: ["services"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: ape-ingress-manager-rolebinding
subjects:
- kind: ServiceAccount
  name: ape-ingress-applier
  namespace: ape-platform-dev
roleRef:
  kind: ClusterRole
  name: ape-ingress-manager-role
  apiGroup: rbac.authorization.k8s.io
