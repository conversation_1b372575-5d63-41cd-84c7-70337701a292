import { Injectable } from "@nestjs/common";
import { InjectRepo } from "nestjs-typeorm3-kit";
import { AccountRepo } from "~/domains/primary/account/account.repo";
import {AccountLstDto, AccountCreateDto, AccountUpdateDto, AccountDetailsDto, ResetPasswordAccountDto} from "./dto/account.dto";
import { TenantRepo } from "~/domains/primary/tenant/tenant.repo";
import { NSAccount } from "~/common/enums";
import { ILike } from "typeorm";
import securityHelper from "~/@core/helpers/security.helper";

@Injectable()
export class AccountService {
    constructor(
        @InjectRepo(AccountRepo)
        readonly accountRepo: AccountRepo,
        @InjectRepo(TenantRepo)
        readonly tenantRepo: TenantRepo,
    ) {}

    async list(params: AccountLstDto) {
        const { pageIndex, pageSize, email, status, type, tenantId, isMasterTenant, searchValue, domain } = params;

        let where: any = {};
        if (email) where.email = email;
        if (status) where.status = status;
        if (type && type !== "all") where.type = type;
        if (tenantId) where.tenantId = tenantId;
        if (isMasterTenant !== undefined) where.isMasterTenant = isMasterTenant;
        if (domain) {
            // Tìm Tenant theo domain
            const tenant = await this.tenantRepo.findOne({ where: { id: domain } });
            if (tenant) {
                where.tenantId = tenant.id;
            }
        }
        if (searchValue) {
            // Search by username OR fullName
            where = [
                { ...where, username: ILike(`%${searchValue}%`) },
                { ...where, fullName: ILike(`%${searchValue}%`) }
            ];
        }

        const { data, total } = await this.accountRepo.findPagination({
            where,
            order: { createdDate: 'DESC' },
        }, {
            pageIndex,
            pageSize,
        });

        const tenants = await this.tenantRepo.find();
        const mapping = data.map(item => {
            const tenant = tenants.find(t => t.id === item.tenantId);
            return {
                ...item,
                tenantName: tenant ? tenant.name : null,
            };
        });
        return { data: mapping, total };
    }

    async create(dto: AccountCreateDto) {
        // Implement account creation logic
        const password = await securityHelper.hash(dto.password);
        dto.password = password;
        return this.accountRepo.save(dto);
    }
    async update(dto: AccountUpdateDto) {
        const { id } = dto;
        // Validate the account exists before updating
        const account = await this.accountRepo.findOneById(id);
        if (!account) {
            throw new Error(`Account with ID ${id} not found`);
        }
        // Implement account update logic
        return this.accountRepo.update(id, dto);
    }


    async details(id: string) {
        // Implement logic to fetch account details
        return this.accountRepo.findOne({ where: { id } });
    }
 
    // Reset Password
    async resetPassword(body: ResetPasswordAccountDto) {
        const { accountId, newPassword, confirmNewPassword } = body;
        // Validate the account exists before resetting password
        const account = await this.accountRepo.findOne({
            where: { id: accountId }
        });
        if (!account) {
            throw new Error(`Account with ID ${accountId} not found`);
        }
        // Implement password reset logic
        account.password = newPassword; // Hash the password in a real application
        return this.accountRepo.save(account);
    }

    async inactive(id: string) {
        const account = await this.accountRepo.findOne({ where: { id } });
        if (!account) {
            throw new Error(`Account with ID ${id} not found`);
        }
        account.status = NSAccount.EStatus.INACTIVE;
        return this.accountRepo.save(account);
    }

    async active(id: string) {
        const account = await this.accountRepo.findOne({ where: { id } });
        if (!account) {
            throw new Error(`Account with ID ${id} not found`);
        }
        account.status = NSAccount.EStatus.ACTIVE;
        return this.accountRepo.save(account);
    }
}
