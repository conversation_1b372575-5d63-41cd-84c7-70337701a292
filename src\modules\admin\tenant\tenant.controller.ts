import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { TenantService } from './tenant.service';
import { Body, Req, Param, Query } from '@nestjs/common';
import {
    RegisterTenantReq,
    ListTenantReq,
    UpdateTenantReq,
    UpdatePassword,
    DetailsTenantReq,
    TenantAccountReq,
} from './dto';
import { Request } from 'express';

@DefController('tenants')
export class TenantController {
    constructor(private readonly tenantService: TenantService) {}

    @DefPost('register', {
        summary: 'Create tenant and make root account',
    })
    register(@Body() body: RegisterTenantReq) {
        return this.tenantService.register(body);
    }

    @DefPost('update', {
        summary: 'Update tenant',
    })
    update(@Body() body: UpdateTenantReq) {
        return this.tenantService.update(body);
    }

    // reset password
    @DefPost('reset-password', {
        summary: 'Reset password',
    })
    resetPassword(@Body() body: UpdatePassword) {
        return this.tenantService.resetPassword(body);
    }

    @DefGet('list', {
        summary: 'List tenants',
    })
    list(@Query() params: ListTenantReq) {
        return this.tenantService.list(params);
    }

    @DefGet('details', {
        summary: 'Details tenant',
    })
    details(@Query() queries: DetailsTenantReq) {
        const { tenantId } = queries;
        return this.tenantService.details(tenantId);
    }

    @DefGet('list-applications', {
        summary: 'List applications',
    })
    listApplications(@Req() req: Request) {
        const domain = req.protocol + '://' + req.get('host');
        return this.tenantService.listApplications(domain);
    }

    //inactive
    @DefPost('inactive', {
        summary: 'Inactive tenant',
    })
    inactive(@Body() body: { id: string }) {
        return this.tenantService.inactive(body.id);
    }

    //active
    @DefPost('active', {
        summary: 'Active tenant',
    })
    active(@Body() body: { id: string }) {
        return this.tenantService.active(body.id);
    }

    // @DefPost('information', {
    //     summary: 'Information tenant',
    // })
    // getInformation(@Body() body: TenantAccountReq) {
    //     return this.tenantService.getInformation(body);
    // }

    @DefPost('get-user-activities-stats', {
        summary: 'Get user activities stats',
    })
    test(@Body() body: TenantAccountReq) {
        return this.tenantService.getUserActivitiesStats(body);
    }

    //list account
    @DefPost('list-account-activities', {
        summary: 'List account',
    })
    listAccountActivity(@Body() body: TenantAccountReq) {
        return this.tenantService.listAccountActivity(body);
    }
}
