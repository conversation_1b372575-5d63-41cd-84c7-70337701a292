apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ape-authenticator-dev-ingress-host
  namespace: ape-authenticator-dev
  annotations:
    kubernetes.io/ingress.class: 'nginx'
    nginx.ingress.kubernetes.io/proxy-body-size: '100m'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
spec:
  rules:
    - host: authenticator-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-authenticator-api-dev
                port:
                  number: 80
    - host: authenticator-admin-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-authenticator-admin-dev
                port:
                  number: 80
    - host: authenticator-client-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-authenticator-client-dev
                port:
                  number: 80