
import { PageRequest } from "~/@systems/utils/page.utils";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";
import { NSAccount } from "~/common/enums";

export class AccountLstDto extends PageRequest {
    // Define properties for listing accounts, e.g., pagination, filters

    @ApiPropertyOptional({ description: 'Filter by username' })
    @IsOptional()
    searchValue?: string

    @ApiPropertyOptional({ description: 'Filter by username' })
    @IsOptional()
    username?: string;

    @ApiPropertyOptional({ description: 'Filter by email' })
    @IsOptional()
    email?: string;

    @ApiPropertyOptional({ description: 'Filter by status' })
    @IsOptional()
    status?: string;

    @ApiPropertyOptional({ description: 'Filter by type' })
    @IsOptional()
    type?: string;

    @ApiPropertyOptional({ description: 'Filter by full name' })
    @IsOptional()
    fullName?: string;

    @ApiPropertyOptional({ description: 'Filter by tenant ID' })
    @IsOptional()
    tenantId?: string;

    @ApiPropertyOptional({ description: 'Filter by domain' })
    @IsOptional()
    domain?: string;

    @ApiPropertyOptional({ description: 'Filter by isMasterTenant' })
    @IsOptional()
    isMasterTenant?: boolean;
}
export class AccountCreateDto {
    // Define properties for creating an account, e.g., username, password, email
    @ApiProperty({ description: 'Username' })
    @IsNotEmpty()
    username: string;

    @ApiProperty({ description: 'Password' })
    @IsNotEmpty()
    password: string;

    @ApiProperty({ description: 'Password Again' })
    @IsNotEmpty()
    passwordAgain: string;

    @ApiProperty({ description: 'Full Name' })
    @IsNotEmpty()
    fullName: string;

    @ApiProperty({ description: 'Tenant ID' })
    @IsNotEmpty()
    tenantId: string;

    @ApiProperty({ description: 'Status' })
    @IsNotEmpty()
    status: NSAccount.EStatus;

    @ApiProperty({ description: 'Type' })
    @IsNotEmpty()
    type: NSAccount.EType = NSAccount.EType.TENANT_USER;

    @ApiProperty({ description: 'Avatar' })
    @IsOptional()
    avatar?: string;
}

export class AccountUpdateDto extends AccountCreateDto {
    @ApiProperty({ description: 'ID of the account to update' })
    @IsNotEmpty()
    id: string;
    // Define properties for updating an account, e.g., username, email, status
}

export class AccountDetailsDto {
    @ApiProperty({ description: 'ID of the account to update' })
    @IsNotEmpty()
    accountId: string;
}

export class ResetPasswordAccountDto {
    @ApiProperty({ description: 'ID of the account to reset password' })
    @IsNotEmpty()
    accountId: string;

    @ApiProperty({ description: 'New password' })
    @IsNotEmpty()
    newPassword: string;

    @ApiProperty({ description: 'Confirm new password' })
    @IsNotEmpty()
    confirmNewPassword: string;
}