import { DefController, DefGet, DefPost } from "nestjs-typeorm3-kit";
import { IngressService, TenantConfig } from "./ingress.service";
import { Param, Query, Body } from "@nestjs/common";

@DefController('ingress')
export class IngressController {
    constructor(private readonly ingressService: IngressService) { }

    // @DefPost('create', { summary: 'Create a new ingress' })
    // async createIngress() {
    //     const data = {
    //         name: 'test-ingress-apetechs',
    //         namespace: 'ape-platform-dev',
    //         adminDomain: 'test-ingress-apetechs.apetechs.co',
    //         adminService: 'ape-platform-admin-dev',
    //     }
    //     return this.ingressService.applyTenantIngress(data);
    // }
}