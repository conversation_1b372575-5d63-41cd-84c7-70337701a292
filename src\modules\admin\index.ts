import { MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { ChildModule, lazyLoadClasses } from 'nestjs-typeorm3-kit';
import { PREFIX_MODULE } from '../config-module';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { APP_GUARD } from '@nestjs/core';
import { join } from 'path';
import { AdminMiddleware } from './admin.middleware';
import { RolesGuard } from '~/@systems/guard/roles.guard';

const controllers = lazyLoadClasses(join(__dirname), ['.controller']);
const services = lazyLoadClasses(join(__dirname), ['.service']);

@ChildModule({
    prefix: PREFIX_MODULE.admin,
    imports: [PrimaryRepoModule],
    providers: [{
        provide: APP_GUARD, useClass: RolesGuard
    },...services],
    controllers: [...controllers],
})
export class AdminModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(AdminMiddleware)
            .exclude(
                { path: `${PREFIX_MODULE.admin}/auth/login`, method: RequestMethod.POST },
            )
            .forRoutes({
                path: `${PREFIX_MODULE.admin}/*`,
                method: RequestMethod.ALL,
            });
    }
}
