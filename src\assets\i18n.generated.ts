/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "account": {
        "not_found": string;
        "password_not_match": string;
    };
    "application": {
        "not_found": string;
    };
    "auth_client": {
        "account_not_active": string;
        "account_not_existed": string;
        "invalid_client_credentials_or_redirect_uri": string;
        "tenant_not_found": string;
        "invalid_password": string;
        "account_type_not_allowed": string;
        "invalid_redirect_uri": string;
    };
    "enum": {
        "NSMember": {
            "EAddressType": {
                "HOME": string;
                "COMPANY": string;
            };
            "EMembershipType": {
                "PERSONAL": string;
                "ENTERPRISE": string;
            };
            "EMemberType": {
                "MEMBER": string;
                "COLLABORATOR": string;
                "POST_OFFICE": string;
            };
            "EStatus": {
                "INACTIVE": string;
                "ACTIVE": string;
                "WAITING_FOR_VERIFY": string;
                "DELETED": string;
                "WAITING_FOR_APPROVE": string;
            };
            "EBusinessType": {
                "LED_ADVERTISEMENT": string;
                "SOCIAL_SECURITY_CARD": string;
                "TOURISM": string;
                "HEALTH": string;
                "SME360": string;
            };
        };
    };
    "member_auth": {
        "login": {
            "error": {
                "member_not_existed": string;
                "wrong_password": string;
            };
        };
        "register": {
            "error": {
                "member_existed": string;
                "tenant_existed": string;
            };
        };
    };
    "tenant": {
        "not_found": string;
        "tax_code_exists": string;
        "domain_exists": string;
        "domain_required": string;
        "tax_code_required": string;
        "username_required": string;
        "password_required": string;
    };
    "validation": {
        "NOT_EMPTY": string;
        "INVALID_EMAIL": string;
        "INVALID_BOOLEAN": string;
        "MIN": string;
        "MAX": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
