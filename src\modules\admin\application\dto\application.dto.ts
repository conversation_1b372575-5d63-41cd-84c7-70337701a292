import { IsNotEmpty, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PageRequest } from '~/@systems/utils/page.utils';
import { NSAccount, NSApplication } from '~/common/enums';

export class CreateApplicationDto {
    @ApiProperty({ example: 'CRM', description: 'Mã ứng dụng' })
    @IsNotEmpty()
    code: string;

    @ApiProperty({ description: 'Tên ứng dụng' })
    @IsNotEmpty()
    name: string;

    @ApiPropertyOptional({ description: 'Mô tả ứng dụng' })
    @IsOptional()
    description?: string;

    @ApiPropertyOptional({ description: 'Tên miền của ứng dụng' })
    @IsOptional()
    clientId?: string;

    @ApiPropertyOptional({ description: 'Mã bí mật của ứng dụng' })
    @IsOptional()
    clientSecret?: string;

    @ApiProperty({ description: 'Redirect URIs' })
    @IsOptional()
    redirectUris?: string[];

    @ApiPropertyOptional({ description: 'Trạng thái' })
    @IsOptional()
    status?: NSApplication.EStatus;

    @ApiPropertyOptional({
        description:
            'Loại tài khoản được phép truy cập, nếu null hoặc mảng rỗng thì là cho tất cả truy cập',
        enum: NSAccount.EType,
    })
    @IsOptional()
    allowedAccountTypes?: NSAccount.EType[];
}

export class ApplicationListDto extends PageRequest {
    @ApiPropertyOptional({ description: 'Mã ứng dụng' })
    @IsOptional()
    code?: string;

    @ApiPropertyOptional({ description: 'Tên ứng dụng' })
    @IsOptional()
    name?: string;

    @ApiPropertyOptional({ description: 'Trạng thái' })
    @IsOptional()
    status?: NSApplication.EStatus;

    @ApiPropertyOptional({ example: '2021-01-01', description: 'Ngày tạo từ' })
    @IsOptional()
    createdDateFrom?: string;

    @ApiPropertyOptional({ example: '2021-01-01', description: 'Ngày tạo đến' })
    @IsOptional()
    createdDateTo?: string;

    @ApiPropertyOptional({ description: 'Tìm kiếm' })
    @IsOptional()
    searchValue?: string;
}

export class UpdateApplicationDto extends CreateApplicationDto {
    @ApiProperty({ example: 'uuid', description: 'Mã ứng dụng' })
    @IsNotEmpty()
    id: string;
}
