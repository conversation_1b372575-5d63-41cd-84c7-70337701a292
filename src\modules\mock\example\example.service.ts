import { Injectable } from '@nestjs/common';
import { DefTransaction } from 'nestjs-typeorm3-kit';
import { ListExampleReq } from './dto';
import { ExampleRepo, ExampleEntity } from '~/domains/primary';
import { ILike } from 'typeorm';

@Injectable()
export class ExampleService {
    constructor(private readonly exampleRepo: ExampleRepo) {}

    async getData(params: ListExampleReq) {
        return { moduleName: 'Example', data: params };
    }

    list(params: ListExampleReq) {
        return this.exampleRepo.findPagination(
            {
                where: {
                    ...(params.keyword ? { stringValue: ILike(`%${params.keyword}%`) } : {}),
                },
                order: { createdDate: 'DESC' },
            },
            params,
        );
    }

    async create() {
        const totalRecord = await this.exampleRepo.count();
        const numberOfInsert = 10;
        const fakeData: Partial<ExampleEntity>[] = Array.from(
            { length: numberOfInsert },
            (_, i) => ({
                stringValue: `Example ${totalRecord + i + 1}`,
                numberValue: totalRecord + i + 1,
                booleanValue: (totalRecord + i + 1) % 2 === 0 ? true : false,
                dateValue: new Date(),
                dateTimeValue: new Date(),
                selectValues: ['SELECT01', 'SELECT02'],
                jsonObjectValue: { key01: 'value01', key02: 'value02' },
                jsonArrayValue: ['record01', 'record02'],
            }),
        );
        return this.exampleRepo.saves(fakeData);
    }
}
