import { ApiProperty } from '@nestjs/swagger';
import { Entity, Column } from 'typeorm';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('tenant')
export class TenantEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Tên tenant' })
  @Column({ length: 255 })
  name: string;

  @ApiProperty({ description: 'Mã doanh nghiệp' })
  @Column({ length: 255, unique: true })
  domain: string;

  @ApiProperty({ description: 'Logo' })
  @Column({ length: 1024, nullable: true })
  logoUrl?: string;

  @ApiProperty({ description: 'Trạng thái' })
  @Column({ default: 'ACTIVE' })
  status: string;

  @ApiProperty({ description: 'Địa chỉ' })
  @Column({ length: 1024, nullable: true })
  address: string;

  @ApiProperty({ description: 'Website' })
  @Column({ length: 1024, nullable: true })
  website: string;

  @ApiProperty({ description: '<PERSON><PERSON> điện thoại' })
  @Column({ length: 1024, nullable: true })
  phone: string;

  @ApiProperty({ description: 'Email' })
  @Column({ length: 1024, nullable: true })
  email: string;

  @ApiProperty({ description: 'Mô tả' })
  @Column({ type: 'text', nullable: true })
  description: string;

  @ApiProperty({ description: 'Mã số thuế' })
  @Column({ length: 1024, nullable: true })
  taxCode: string;
}
