import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { Request, Response } from 'express';
import { KEY_HEADER } from '~/common/constants';
import { nanoid } from 'nanoid';
import { configEnv } from '~/@config/env';

@Injectable()
export class PublicMiddleware implements NestMiddleware {
    constructor(
    ) { }

    async use(req: Request, res: Response, next: Function) {
        console.log("=========== PublicMiddleware ===========");
        const { PUBLIC_API_KEY } = configEnv();
        try {
            const { headers = {} } = req;
            if (!headers || !headers[KEY_HEADER.PUBLIC_API_KEY]) {
                throw new UnauthorizedException('Unauthorized');
            }
            const accessTokenBearer = headers[KEY_HEADER.PUBLIC_API_KEY] as string;
            const accessToken = accessTokenBearer.replace('ApiKey', '').trim();
            if (!accessToken) {
                throw new UnauthorizedException('Unauthorized');
            }
            if (!PUBLIC_API_KEY || PUBLIC_API_KEY !== accessToken) {
                throw new UnauthorizedException('Unauthorized');
            }
            next();
        } catch (error) {
            console.log(error);
            next(new UnauthorizedException('Unauthorized'));
        }
    }
}

@Injectable()
export class SessionMiddleware implements NestMiddleware {
    use(req: Request, res: Response, next: Function) {
        if (!req.cookies?.sessionId) {
            const sessionId = nanoid();
            res.cookie('sessionId', sessionId, {
                httpOnly: true,
                maxAge: 30 * 24 * 60 * 60 * 1000, // 30 ngày
                sameSite: 'lax',
            });
            req['sessionId'] = sessionId;
        } else {
            req['sessionId'] = req.cookies.sessionId;
        }
        next();
    }
}