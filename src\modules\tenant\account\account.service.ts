import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import {
    AccountRepo,
    AuthorizationCodeRepo,
    TokenRepo,
    TenantApplicationRepo,
    ApplicationRepo,
    TenantRepo,
    AccountEntity,
    AccountApplicationRepo,
} from '~/domains/primary';
import { JwtService } from '@nestjs/jwt';
import {
    ChangePasswordByAccountReq,
    CreateAccountReq,
    ListAccountReq,
    ListRegisteredAppByAccountReq,
    LockAccountReq,
    RegisterAppByAccountReq,
} from './dto';
import securityHelper from '~/@core/helpers/security.helper';
import tenantAccountSession from '../@guards/tenant-account/tenant-account.session';
import { NSAccount } from '~/common/enums';
import { BusinessException } from '~/@systems/exceptions';
import { systemHelper } from '~/common/helpers';
import { In } from 'typeorm';

@Injectable()
export class AccountService {
    private readonly logger = new Logger(AccountService.name);

    constructor(
        private readonly jwtService: JwtService,
        @InjectRepo(TenantRepo)
        private readonly tenantRepo: TenantRepo,
        @InjectRepo(AccountRepo)
        private readonly accountRepo: AccountRepo,
        @InjectRepo(TenantApplicationRepo)
        private readonly tenantAppRepo: TenantApplicationRepo,
        @InjectRepo(AuthorizationCodeRepo)
        private readonly codeRepo: AuthorizationCodeRepo,
        @InjectRepo(TokenRepo)
        private readonly tokenRepo: TokenRepo,
        @InjectRepo(ApplicationRepo)
        private readonly applicationRepo: ApplicationRepo,
        @InjectRepo(AccountApplicationRepo)
        private readonly accountAppRepo: AccountApplicationRepo,
    ) {}

    private clearPrivateAccountData(account: AccountEntity) {
        const { password, createdBy, updatedBy, createdDate, updatedDate, ...rest } = account;
        return rest;
    }

    async create(body: CreateAccountReq) {
        const { tenantId } = tenantAccountSession;
        const type = body?.type || NSAccount.EType.TENANT_USER;
        const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
        if (!tenant) {
            throw new BusinessException('tenant_account.tenant_not_found');
        }
        const username = systemHelper.generateUserNameAccount(body.username, tenant.domain);
        const account = await this.accountRepo.findOne({
            where: {
                username,
            },
        });
        if (account) {
            throw new BusinessException('tenant_account.username_exists');
        }
        const newAccount = await this.accountRepo.save({
            ...body,
            username,
            type,
            password: await securityHelper.hash(body.password),
            tenantId,
            status: NSAccount.EStatus.ACTIVE,
        });
        return this.clearPrivateAccountData(newAccount);
    }

    async list(body: ListAccountReq) {
        const sql = `
            SELECT 
                acc."id", 
                acc."tenantId",
                acc."username",
                acc."fullName",
                acc."avatar",
                acc."type",
                acc."status",
                acc."createdDate",
                acc."updatedDate",
                COALESCE ( json_agg ( DISTINCT app.* ) FILTER ( WHERE app."id" IS NOT NULL ), '[]' ) AS "applications"
            FROM "account" acc
            LEFT JOIN "account_application" aa ON acc."id" = aa."accountId"
            LEFT JOIN "application" app ON aa."applicationId" = app.id
            WHERE acc."tenantId" = '${tenantAccountSession.tenantId}'
            GROUP BY acc."id"
            ORDER BY acc."createdDate" DESC
        `;
        const { data, total } = await this.accountRepo.queryPagination(sql, body);
        return {
            data: data.map(item => {
                const { applications = [], ...rest } = item;
                return {
                    ...rest,
                    applications: applications.map((app: any) => ({
                        id: app.id,
                        name: app.name,
                        code: app.code,
                        description: app.description,
                    })),
                };
            }),
            total,
        };
    }

    async changePasswordByAccount(body: ChangePasswordByAccountReq) {
        const { id, password } = body;
        const account = await this.accountRepo.findOne({ where: { id } });
        if (!account) {
            throw new BusinessException('tenant_account.not_found');
        }
        account.password = await securityHelper.hash(password);
        await this.accountRepo.save(account);
        return this.clearPrivateAccountData(account);
    }

    async lock(body: LockAccountReq) {
        const { id } = body;
        const account = await this.accountRepo.findOne({ where: { id } });
        if (!account) {
            throw new BusinessException('tenant_account.not_found');
        }
        account.status = NSAccount.EStatus.LOCKED;
        await this.accountRepo.save(account);
        return this.clearPrivateAccountData(account);
    }

    async unlock(body: LockAccountReq) {
        const { id } = body;
        const account = await this.accountRepo.findOne({ where: { id } });
        if (!account) {
            throw new BusinessException('tenant_account.not_found');
        }
        account.status = NSAccount.EStatus.ACTIVE;
        await this.accountRepo.save(account);
        return this.clearPrivateAccountData(account);
    }

    async registerAppByAccount(body: RegisterAppByAccountReq) {
        const { accountId, appCodes } = body;
        const account = await this.accountRepo.findOne({ where: { id: accountId } });
        if (!account) {
            throw new BusinessException('tenant_account.not_found');
        }
        if (account.tenantId !== tenantAccountSession.tenantId) {
            throw new BusinessException('tenant_account.not_found');
        }

        await this.accountAppRepo.delete({
            accountId,
        });
        if (!appCodes || appCodes.length === 0) {
            return [];
        }
        const listAppNewRegister = await this.applicationRepo.find({
            select: ['id', 'name', 'code', 'description'],
            where: {
                code: In(appCodes),
            },
        });
        await this.accountAppRepo.save(
            listAppNewRegister.map(app => ({
                accountId,
                applicationId: app.id,
                tenantId: account.tenantId,
            })),
        );
        return listAppNewRegister;
    }

    async listRegisteredAppByAccount(body: ListRegisteredAppByAccountReq) {
        const { accountId } = body;
        const account = await this.accountRepo.findOne({ where: { id: accountId } });
        if (!account) {
            throw new BusinessException('tenant_account.not_found');
        }
        if (account.tenantId !== tenantAccountSession.tenantId) {
            throw new BusinessException('tenant_account.not_found');
        }
        const sql = `
            SELECT 
                a.id, 
                a.name,
                a.code, 
                a."description"
            FROM "account_application" aa
            INNER JOIN "application" a ON aa."applicationId" = a.id 
            WHERE aa."accountId" = '${accountId}'
        `;
        const result = await this.accountAppRepo.query(sql);
        return result;
    }
}
