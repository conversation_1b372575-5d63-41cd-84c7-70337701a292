import { Injectable } from "@nestjs/common";
import { InjectRepo } from "nestjs-typeorm3-kit";
import { AccountRepo, ApplicationRepo, TenantRepo } from "~/domains/primary";
import { AnalyticsQueryDto, ChartJsLineData, DashboardStatsDto, OverviewAnalytics } from "./dto";
import { getRange } from "~/common/helpers";
import { NSAccount } from "~/common/enums";
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
dayjs.extend(isoWeek);


@Injectable()
export class DashboardService {
    constructor(
        @InjectRepo(TenantRepo)
        private readonly tenantRepo: TenantRepo,
        @InjectRepo(ApplicationRepo)
        private readonly applicationRepo: ApplicationRepo,
        @InjectRepo(AccountRepo)
        private readonly accountRepo: AccountRepo
    ) {
    }

    async getStats(): Promise<DashboardStatsDto> {
        const [totalTenant, totalApplication, totalAccount] = await Promise.all([
            this.tenantRepo.count(),
            this.applicationRepo.count(),
            this.accountRepo.count(),
        ]);
        return new DashboardStatsDto({
            totalTenant,
            totalApplication,
            totalAccount,
        });
    }

    async getTopTenantsByUserCount(): Promise<{ labels: string[]; datasets: number[] }> {
        const sql = `
            SELECT t.id as "tenantId", t.name as "tenantName", COUNT(a.id) as "userCount"
            FROM tenant t
            LEFT JOIN account a ON a."tenantId" = t.id
            GROUP BY t.id, t.name
            ORDER BY "userCount" DESC
            LIMIT 5
        `;
        const result = await this.tenantRepo.query(sql);
        const labels = result.map(row => row.tenantName);
        const data = result.map(row => Number(row.userCount));
        return { labels, datasets: data };
    }

    //#region Account
    async overview(q: AnalyticsQueryDto): Promise<OverviewAnalytics> {
        const { start, end } = getRange(q.period);

        const whereTenant = q.tenantId ? { tenantId: q.tenantId } : {};

        const [totalUsers, newUsers, tenantMasters, tenantUsers] = await Promise.all([
            this.accountRepo.count({ where: { ...whereTenant } }),
            this.accountRepo.count({
                where: {
                    ...whereTenant,
                    createdAt: {
                        $gte: start.toDate() as any,
                        $lte: end.toDate() as any,
                    } as any, // (hoặc Between(start.toDate(), end.toDate()))
                } as any,
            }),
            this.accountRepo.count({ where: { ...whereTenant, type: NSAccount.EType.TENANT_MASTER as any } }),
            this.accountRepo.count({ where: { ...whereTenant, type: NSAccount.EType.TENANT_USER as any } }),
        ]);

        const pie = [
            { type: 'Nhân viên', count: tenantUsers, percent: totalUsers ? tenantUsers / totalUsers : 0 },
            { type: 'Doanh nghiệp', count: tenantMasters, percent: totalUsers ? tenantMasters / totalUsers : 0 },
            // Nếu muốn thêm CUSTOMER:
            // { type: 'Khách hàng', count: customers, percent: ... }
        ];

        return { totalUsers, newUsers, tenantMasters, tenantUsers, pie };
    }

    async growth(q: AnalyticsQueryDto): Promise<ChartJsLineData> {
        const { start, end, granularity } = getRange(q.period);
        const tz = q.tz || 'Asia/Ho_Chi_Minh';

        const qb = this.accountRepo.createQueryBuilder('a');

        if (q.tenantId) qb.andWhere('a.tenant_id = :tenantId', { tenantId: q.tenantId });

        qb.andWhere('a.created_at >= :start AND a.created_at <= :end', {
            start: start.toDate(),
            end: end.toDate(),
        });

        const truncUnit = granularity === 'day' ? 'day' : 'month';
        const fmt = granularity === 'day' ? 'YYYY-MM-DD' : 'YYYY-MM';

        qb
            .select(`
      to_char(
        date_trunc('${truncUnit}', a.created_at AT TIME ZONE :tz),
        '${fmt}'
      ) AS bucket
    `)
            .addSelect('COUNT(*)', 'count')
            .setParameter('tz', tz)
            .groupBy('bucket')
            .orderBy('bucket', 'ASC');

        const rows = await qb.getRawMany<{ bucket: string; count: string }>();
        const countsMap = new Map<string, number>(
            rows.map(r => [r.bucket, Number(r.count)])
        );

        // Tạo list bucket đầy đủ + label hiển thị đẹp cho UI
        const labels: string[] = [];
        const data: number[] = [];

        if (granularity === 'day') {
            // đi từng ngày
            for (let d = start.clone(); d.isBefore(end) || d.isSame(end, 'day'); d = d.add(1, 'day')) {
                const key = d.format('YYYY-MM-DD');
                labels.push(d.format('DD/MM'));      // label cho Chart.js
                data.push(countsMap.get(key) ?? 0);
            }
        } else {
            // đi từng tháng
            for (let d = start.clone().startOf('month'); d.isBefore(end) || d.isSame(end, 'month'); d = d.add(1, 'month')) {
                const key = d.format('YYYY-MM');
                labels.push(d.format('MM/YYYY'));     // label cho Chart.js
                data.push(countsMap.get(key) ?? 0);
            }
        }

        return {
            labels,
            datasets: [
                {
                    label: 'Người dùng mới',
                    data,
                },
            ],
        };
    }
    //#endregion

    //#region Account Overview with Tenant
    //#endregion
}