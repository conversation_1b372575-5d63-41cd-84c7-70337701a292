// ingress.service.ts
import { CoreV1Api, KubeConfig, NetworkingV1Api, V1Ingress } from '@kubernetes/client-node';
import { Injectable } from "@nestjs/common";
import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
export interface TenantConfig {
  name: string; // shorthand name
  namespace: string; // theo application, mỗi application 1 namespace, có namespace dev và prod
  adminDomain: string; //name.apetechs.co
  adminService: string; // có dev và prod riêng (ape-platform-admin-dev, ape-platform-client-dev) hoặc là (ape-platform-admin-prod, ape-platform-client-prod)
}

const INGRESS_TEMPLATE_PATH = path.join(__dirname, '../../../assets/ingressTemplate.yaml');
@Injectable()
export class IngressService {
  private k8sApi: NetworkingV1Api;
  private kubeconfig: KubeConfig;
    constructor(
    ) {
       this.kubeconfig = new KubeConfig();
       this.kubeconfig.loadFromDefault();
       this.k8sApi = this.kubeconfig.makeApiClient(NetworkingV1Api);
    }
    async applyTenantIngress(tenantConfig: TenantConfig) {
      const ingressBody: V1Ingress = await this.createIngressObject(tenantConfig);
      const ingressName = ingressBody.metadata.name;
      const namespace = tenantConfig.namespace;
    
      try {

        // 1. Kiểm tra và tạo namespace nếu chưa tồn tại
        await this.ensureNamespaceExists(namespace);
        // 2. Try to CREATE the Ingress
        console.log(`Applying Ingress for tenant: ${tenantConfig.name} in namespace: ${namespace}`);
        await this.k8sApi.createNamespacedIngress({namespace, body: ingressBody});
        console.log('Ingress applied successfully!');
      } catch (error: any) {
        if (error.body && error.body.reason === 'AlreadyExists') {
         console.log(`Ingress for ${tenantConfig.name} already exists. Attempting to update...`);
        } else {
          // Handle other types of errors
          console.error('Error applying Ingress to Kubernetes:', error.body || error.message);
          throw error;
        }
      }
    }

    private async ensureNamespaceExists(namespace: string) {
      const coreV1Api = this.kubeconfig.makeApiClient(CoreV1Api);
      try {
        await coreV1Api.readNamespace({name: namespace});
        console.log(`Namespace '${namespace}' already exists.`);
      } catch (error: any) {
        if (error.body && error.body.reason === 'NotFound') {
          console.log(`Namespace '${namespace}' not found. Creating it...`);
          // await coreV1Api.createNamespace({body: {metadata: {name: namespace}}});
          // console.log(`Namespace '${namespace}' created successfully.`);
        } else {
          // Xử lý các lỗi khác ngoài "NotFound"
          console.error(`Error checking/creating namespace '${namespace}':`, error.body || error.message);
          throw error;
        }
      }
    }

    async createIngressObject(tenantConfig: TenantConfig) {
      try {
        const templateContent = fs.readFileSync(INGRESS_TEMPLATE_PATH, 'utf8');
    
        const ingressYaml = templateContent
          .replace(/\${TENANT_NAME}/g, tenantConfig.name)
          .replace(/\${NAMESPACE}/g, tenantConfig.namespace)
          .replace(/\${TENANT_ADMIN_DOMAIN}/g, tenantConfig.adminDomain)
          .replace(/\${TENANT_ADMIN_SERVICE}/g, tenantConfig.adminService)
    
        const ingressObject = yaml.load(ingressYaml);
    
        return ingressObject;
      } catch (error) {
        console.error('Error creating Ingress object from template:', error);
        throw error;
      }
    }
}
