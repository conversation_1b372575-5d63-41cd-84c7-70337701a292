# ingressTemplate.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${TENANT_NAME}-ingress
  namespace: ${NAMESPACE}
  annotations:
    kubernetes.io/ingress.class: 'nginx'
    nginx.ingress.kubernetes.io/proxy-body-size: '100m'
    nginx.ingress.kubernetes.io/proxy-read-timeout: '300'
    nginx.ingress.kubernetes.io/proxy-send-timeout: '300'
spec:
  rules:
    - host: ${TENANT_ADMIN_DOMAIN}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ${TENANT_ADMIN_SERVICE}
                port:
                  number: 80