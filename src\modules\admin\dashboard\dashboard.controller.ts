import { DefController, DefGet } from "nestjs-typeorm3-kit";
import { DashboardService } from "./dashboard.service";
import { AnalyticsQueryDto, ChartJsLineData, DashboardStatsDto, GrowthAnalytics, OverviewAnalytics } from "./dto";
import { Query } from "@nestjs/common";


@DefController("dashboard")
export class DashboardController {
    constructor(private readonly dashboardService: DashboardService) { }

    @DefGet("stats", { summary: "Get dashboard statistics" })
    async getStats(): Promise<DashboardStatsDto> {
        return this.dashboardService.getStats();
    }

    @DefGet("top-tenants", { summary: "Top 5 tenants with most users" })
    async getTopTenants() {
        return this.dashboardService.getTopTenantsByUserCount();
    }

    @DefGet("overview", { summary: "Get overview analytics" })
    async getOverview(@Query() query: AnalyticsQueryDto): Promise<OverviewAnalytics> {
        return this.dashboardService.overview(query);
    }

    @DefGet("growth", { summary: "Get growth analytics" })
    async getGrowth(@Query() query: AnalyticsQueryDto): Promise<ChartJsLineData> {
        return this.dashboardService.growth(query);
    }
}