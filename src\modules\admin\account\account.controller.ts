import { DefController, DefGet, DefPost } from "nestjs-typeorm3-kit";
import { AccountService } from "./account.service";
import { AccountLstDto, AccountCreateDto, AccountUpdateDto, AccountDetailsDto, ResetPasswordAccountDto } from "./dto/account.dto";
import { Param, Query, Body } from "@nestjs/common";

@DefController('accounts')
export class AccountController {
    constructor(private readonly accountService: AccountService) { }

    @DefGet('list', {
        summary: 'List accounts',
    })
    async listAccounts(@Query() params: AccountLstDto) {
        return this.accountService.list(params);
    }

    // Tạo nhân viên danh cho user ROOT
    @DefPost('create', { summary: 'Create a new account (employee)' })
    async createAccount(@Body() dto: AccountCreateDto) {
        return this.accountService.create(dto);
    }

    @DefPost('update', { summary: 'Update info account' })
    async updateAccount(@Body() dto: AccountUpdateDto) {
        return this.accountService.update(dto);
    }

    @DefPost('details', { summary: 'Get account details' })
    async getAccountDetails(@Body('id') id: string) {
        return this.accountService.details(id);
    }

    @DefPost('reset-password', { summary: 'Reset account password' })
    async resetAccountPassword(@Body() dto: ResetPasswordAccountDto) {
        return this.accountService.resetPassword(dto);
    }

    // Inactive
    @DefPost('inactive', { summary: 'Inactive account' })
    async inactiveAccount(@Body('id') id: string) {
        return this.accountService.inactive(id);
    }

    @DefPost('active', { summary: 'Active account' })
    async activeAccount(@Body('id') id: string) {
        return this.accountService.active(id);
    }

}
