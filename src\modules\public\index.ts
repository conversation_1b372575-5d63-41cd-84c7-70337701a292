import { MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { ChildModule, lazyLoadClasses } from 'nestjs-typeorm3-kit';
import { PREFIX_MODULE } from '../config-module';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { join } from 'path';
import { ShareModule } from '../@share';
import { PublicMiddleware } from './public.middleware';

const controllers = lazyLoadClasses(join(__dirname), ['.controller']);
const services = lazyLoadClasses(join(__dirname), ['.service']);

@ChildModule({
    prefix: PREFIX_MODULE.public,
    imports: [PrimaryRepoModule, ShareModule],
    providers: [...services],
    controllers: [...controllers],
})
export class PublicModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(PublicMiddleware)
            .forRoutes({
                path: `${PREFIX_MODULE.public}/*`,
                method: RequestMethod.ALL,
            });
    }
}
