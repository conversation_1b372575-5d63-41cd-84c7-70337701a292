import { Body, UseGuards } from '@nestjs/common';
import { AccountService } from './account.service';
import { DefController, DefPost } from 'nestjs-typeorm3-kit';
import {
    ChangePasswordByAccountReq,
    CreateAccountReq,
    ListAccountReq,
    ListRegisteredAppByAccountReq,
    LockAccountReq,
    RegisterAppByAccountReq,
} from './dto';
import { TenantAccountGuard } from '../@guards/tenant-account/tenant-account.guard';

@UseGuards(TenantAccountGuard)
@DefController('accounts')
export class AccountController {
    constructor(private readonly accountService: AccountService) {}

    @DefPost('create')
    async create(@Body() body: CreateAccountReq) {
        return this.accountService.create(body);
    }

    @DefPost('list')
    async list(@Body() body: ListAccountReq) {
        return this.accountService.list(body);
    }

    @DefPost('change-password')
    async changePassword(@Body() body: ChangePasswordByAccountReq) {
        return this.accountService.changePasswordByAccount(body);
    }

    @DefPost('lock')
    async lock(@Body() body: LockAccountReq) {
        return this.accountService.lock(body);
    }

    @DefPost('unlock')
    async unlock(@Body() body: LockAccountReq) {
        return this.accountService.unlock(body);
    }

    @DefPost('register-app-by-account')
    async registerAppByAccount(@Body() body: RegisterAppByAccountReq) {
        return this.accountService.registerAppByAccount(body);
    }

    @DefPost('list-registered-app-by-account')
    async listRegisteredAppByAccount(@Body() body: ListRegisteredAppByAccountReq) {
        return this.accountService.listRegisteredAppByAccount(body);
    }
}
