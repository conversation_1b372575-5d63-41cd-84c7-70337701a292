

const CONCURRENCY_LIMIT = 10; // chỉnh tùy DB

export const limitConcurrency = <TIn, TOut>(
    items: TIn[],
    worker: (item: TIn, index: number) => Promise<TOut>,
    limit = CONCURRENCY_LIMIT,
): Promise<TOut[]> => {
    const ret = new Array<TOut>(items.length);
    let i = 0;
    const runners = Array(Math.min(limit, items.length)).fill(0).map(async () => {
      while (true) {
        const idx = i++;
        if (idx >= items.length) break;
        try {
          ret[idx] = await worker(items[idx], idx);
        } catch (e: any) {
          // giữ nguyên để caller tự xử lý nếu cần
          throw e;
        }
      }
    });
    return Promise.all(runners).then(() => ret);
}

// Tách nhỏ mảng để query IN an toàn
export const chunk = <T,>(arr: T[], size: number) => {
  const out: T[][] = [];
  for (let i = 0; i < arr.length; i += size) out.push(arr.slice(i, i + size));
  return out;
};