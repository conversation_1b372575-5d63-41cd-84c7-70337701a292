import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { configEnv } from '~/@config/env';
import { GUARD_CODE } from './client-auth.guard';

@Injectable()
export class ClientAuthStrategy extends PassportStrategy(Strategy, GUARD_CODE) {
    constructor() {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configEnv().JWT_SECRET,
        });
    }

    async validate(payload: any) {
        return payload;
    }
}
