# Hướng dẫn luồng tạo account,đăng kí application từ TENANT UI với các tài khoản MASTER TENANT

## 1. Đăng nhập với tài khoản MASTER TENANT, các account TENANT_USER hoặc CUSTOMER không có quyền truy cập vào tenant UI

#### Request

```bash
curl --location '{{baseUrl}}/api/client/auth/login' \
--header 'Content-Type: application/json' \
--data-raw '{
    "username": "<EMAIL>",
    "password": "12345",
    "clientId": "{{tenantSettingClientId}}",
    "clientSecret": "{{tenantSettingClientSecret}}",
    "redirectUri": "{{tenantSettingRedirectUri}}"
}'
```

#### Response

```json
{
    "access_token": "<accessToken>",
    "refresh_token": "<refreshToken>",
    "expires_in": 3600,
    "token_type": "Bearer"
}
```

## 2. <PERSON><PERSON><PERSON> tà<PERSON> khoản con

#### Request

```bash
    curl --location '{{baseUrl}}/api/tenant/accounts/create' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <accessToken>' \
--data-raw '{
  "username": "<EMAIL>",
  "password": "12345",
  "fullName": "NV05",
  "avatar": "https://placehold.co/400"
}
'
```

#### Response

```json
{
    "username": "<EMAIL>",
    "fullName": "NV02",
    "avatar": "https://placehold.co/400",
    "type": "TENANT_USER",
    "tenantId": "0080ad1c-10b7-46f7-b130-7754c834de11",
    "status": "ACTIVE",
    "id": "44578fcf-69c6-4d17-80ed-f9e9533b84a2",
    "version": 1
}
```

## 3. Đăng kí application

#### Request

```bash
    curl --location '{{baseUrl}}/api/tenant/applications/register' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer <accessToken>' \
--data-raw '{
  "codes": ["CRM", "PMS"]
}
'
```

#### Response

```json
[
    {
        "tenantId": "0080ad1c-10b7-46f7-b130-7754c834de11",
        "applicationId": "a9147d85-6310-4bb5-853d-5ff79232519c",
        "createdBy": null,
        "updatedBy": null,
        "clientId": null,
        "clientSecret": null,
        "redirectUris": null,
        "tenantDomain": null,
        "id": "09a8bf45-6571-48ae-a31b-9bd46e56874e",
        "createdDate": "2025-08-13T10:59:51.516Z",
        "updatedDate": "2025-08-13T10:59:51.516Z",
        "version": 1,
        "isActive": true,
        "plan": "default"
    }
]
```
