import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { configEnv } from '~/@config/env';
import { GUARD_CODE } from './tenant-account.guard';
import { TenantAccountPayload } from './dto';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { AccountRepo } from '~/domains/primary';
import { NSAccount } from '~/common/enums';
import { ApplicationRepo } from '~/domains/primary';
import { RequestContext } from '~/@core/context';
import { KEY_SESSION_CONTEXT } from '~/common/constants';

@Injectable()
export class TenantAccountStrategy extends PassportStrategy(Strategy, GUARD_CODE) {
    constructor(
        @InjectRepo(AccountRepo) private readonly accountRepo: AccountRepo,
        @InjectRepo(ApplicationRepo) private readonly applicationRepo: ApplicationRepo,
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configEnv().JWT_SECRET,
        });
    }

    async validate(payload: TenantAccountPayload) {
        const account = await this.accountRepo.findOne({
            where: {
                id: payload.sub,
            },
        });
        if (!account) {
            throw new UnauthorizedException('Account not found');
        }
        if (account.status !== NSAccount.EStatus.ACTIVE) {
            throw new UnauthorizedException('Account not active');
        }
        const application = await this.applicationRepo.findOne({
            where: {
                id: payload.applicationId,
            },
        });
        if (!application) {
            throw new UnauthorizedException('Application not found');
        }
        if (application.status !== NSAccount.EStatus.ACTIVE) {
            throw new UnauthorizedException('Application not active');
        }

        if (
            application?.allowedAccountTypes?.length > 0 &&
            !application?.allowedAccountTypes?.includes(account.type)
        ) {
            throw new UnauthorizedException('Account type not allowed');
        }
        delete account.password;
        delete application.clientSecret;

        RequestContext.setAttribute(KEY_SESSION_CONTEXT.TENANT_ACCOUNT_SESSION, {
            ...payload,
            account,
            application,
        });
        return payload;
    }
}
