import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { ILike, In } from 'typeorm';
import { BusinessException } from '~/@systems/exceptions';
import { NSApplication } from '~/common/enums';
import {
    AccountApplicationRepo,
    AccountRepo,
    ApplicationRepo,
    AuthorizationCodeRepo,
    TenantApplicationRepo,
    TenantRepo,
    TokenRepo,
} from '~/domains/primary';
import tenantAccountSession from '../@guards/tenant-account/tenant-account.session';
import {
    ListAccountRegisteredByApplicationReq,
    ListApplicationReq,
    ListRegisteredApplicationReq,
    RegisterApplicationReq,
    UnregisterApplicationReq,
} from './dto';

@Injectable()
export class ApplicationService {
    private readonly logger = new Logger(ApplicationService.name);

    constructor(
        private readonly jwtService: JwtService,
        @InjectRepo(TenantRepo)
        private readonly tenantRepo: TenantRepo,
        @InjectRepo(AccountRepo)
        private readonly accountRepo: AccountRepo,
        @InjectRepo(TenantApplicationRepo)
        private readonly tenantAppRepo: TenantApplicationRepo,
        @InjectRepo(AuthorizationCodeRepo)
        private readonly codeRepo: AuthorizationCodeRepo,
        @InjectRepo(TokenRepo)
        private readonly tokenRepo: TokenRepo,
        @InjectRepo(ApplicationRepo)
        private readonly applicationRepo: ApplicationRepo,
        @InjectRepo(AccountApplicationRepo)
        private readonly accountAppRepo: AccountApplicationRepo,
    ) {}

    async register(body: RegisterApplicationReq) {
        const { tenantId, applicationId } = tenantAccountSession;
        console.log(`=====applicationId=====${applicationId}`);
        const tenant = await this.tenantRepo.findOne({
            where: {
                id: tenantId,
            },
        });
        if (!tenant) {
            throw new BusinessException('Tenant not found');
        }

        const application = await this.applicationRepo.findOne({
            where: {
                id: applicationId,
            },
        });
        if (!application) {
            throw new BusinessException('Application not found');
        }

        const app = await this.applicationRepo.findOne({
            select: ['id', 'name', 'code', 'description'],
            where: {
                code: body.code,
            },
        });
        if (!app) {
            throw new BusinessException('Application not found');
        }
        const tenantApp = await this.tenantAppRepo.findOne({
            where: {
                tenantId,
                applicationId: app.id,
            },
        });
        if (tenantApp) {
            throw new BusinessException('Application already registered');
        }
        await this.tenantAppRepo.save({
            tenantId,
            applicationId: app.id,
        });
        return app;
    }

    async list(body: ListApplicationReq) {
        const { keyword, status } = body;
        const wheres: any = {};
        if (keyword) {
            wheres.name = ILike(`%${keyword}%`);
        }
        if (status) {
            wheres.status = status;
        }
        const { data, total } = await this.applicationRepo.findPagination(
            {
                where: wheres,
                select: ['id', 'name', 'code', 'description'],
                order: { createdDate: 'DESC' },
            },
            { ...body },
        );

        const appIds = data.map(app => app.id);
        const tenantId = tenantAccountSession.tenantId;
        const tenantApps = await this.tenantAppRepo.find({
            where: {
                tenantId,
                applicationId: In(appIds),
            },
        });

        const mappingData = data.map(item => {
            const tenantApp = tenantApps.find(ta => ta.applicationId === item.id);
            return {
                ...item,
                registerStatus: !!tenantApp
                    ? NSApplication.ERegisterStatus.REGISTERED
                    : NSApplication.ERegisterStatus.UNREGISTERED,
            };
        });

        return { data: mappingData, total };
    }

    async listRegistered(body: ListRegisteredApplicationReq) {
        const { keyword } = body;
        const wheres: string[] = [`ta."tenantId" = '${tenantAccountSession.tenantId}'`];
        if (keyword) {
            wheres.push(`name ILIKE '%${keyword}%'`);
        }
        const sql = `
            SELECT app."id", app."name", app."code", app."description", '${NSApplication.ERegisterStatus.REGISTERED}' AS "registerStatus"
            FROM "tenant_application" ta 
            INNER JOIN "application" app ON ta."applicationId" = app.id 
            WHERE ${wheres.join(' AND ')}
        `;
        const data = await this.applicationRepo.query(sql);
        return data;
    }

    async unregister(body: UnregisterApplicationReq) {
        const { code } = body;
        const app = await this.applicationRepo.findOne({
            select: ['id', 'name', 'code', 'description'],
            where: {
                code,
            },
        });
        if (!app) {
            throw new BusinessException('Application not found');
        }
        const tenantApp = await this.tenantAppRepo.findOne({
            where: {
                tenantId: tenantAccountSession.tenantId,
                applicationId: app.id,
            },
        });
        if (!tenantApp) {
            throw new BusinessException('Application not registered');
        }

        // xóa tất cả các account đã đăng ký trong accont_application
        await this.accountAppRepo.delete({
            tenantId: tenantAccountSession.tenantId,
            applicationId: app.id,
        });

        await this.tenantAppRepo.delete({
            tenantId: tenantAccountSession.tenantId,
            applicationId: app.id,
        });
        return app;
    }
    async listAccountRegisteredByApplication(body: ListAccountRegisteredByApplicationReq) {
        const { applicationId } = body;
        const accountApps = await this.accountAppRepo.find({
            where: {
                applicationId,
                tenantId: tenantAccountSession.tenantId,
            },
        });
        const accountIds = accountApps.map(item => item.accountId);
        const accounts = await this.accountRepo.find({
            where: {
                id: In(accountIds),
            },
        });
        return accounts;
    }
}
