import { Injectable, NestMiddleware, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { configEnv } from '~/@config/env';
import { RequestContext } from '~/@core/context';
import { KEY_HEADER, KEY_SESSION_CONTEXT } from '~/common/constants';
import { nanoid } from 'nanoid';
import { UserAdminRepo } from '~/domains/primary';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { AdminSessionDto, LoginAdmin } from './auth/dto/index.dto';

@Injectable()
export class AdminMiddleware implements NestMiddleware {
    constructor(
        private jwtService: JwtService,
        @InjectRepo(UserAdminRepo)
        private userAdminRepo: UserAdminRepo
    ) { }

    async use(req: Request, res: Response, next: Function) {
        console.log("=========== AdminMiddleware ===========");
        const { JWT_SECRET } = configEnv();
        try {
            const { headers = {} } = req;
            if (!headers || !headers[KEY_HEADER.AUTHORIZATION]) {
                throw new UnauthorizedException('Unauthorized');
            }
            const accessTokenBearer = headers[KEY_HEADER.AUTHORIZATION] as string;
            const accessToken = accessTokenBearer.replace('Bearer', '').trim();
            if (!accessToken) {
                throw new UnauthorizedException('Unauthorized');
            }

            try {
                const payload = await this.jwtService.verifyAsync<LoginAdmin>(accessToken, {
                    secret: JWT_SECRET,
                });
                // Check status member
                const admin = await this.userAdminRepo.findOne({ where: { id: payload.sub } });
                if (!admin) {
                    throw new UnauthorizedException('Unauthorized');
                }

                RequestContext.setAttribute<AdminSessionDto>(KEY_SESSION_CONTEXT.ADMIN_SESSION, {
                    accessToken,
                    refreshToken: '',
                    tokenType: 'Bearer',
                    ...payload,
                });
                next();
            } catch (error) {
                console.log(`==========`, error);
                throw new UnauthorizedException('Unauthorized');
            }
        } catch (error) {
            console.log(error);
            next(new UnauthorizedException('Unauthorized'));
        }
    }
}

@Injectable()
export class SessionMiddleware implements NestMiddleware {
    use(req: Request, res: Response, next: Function) {
        if (!req.cookies?.sessionId) {
            const sessionId = nanoid();
            res.cookie('sessionId', sessionId, {
                httpOnly: true,
                maxAge: 30 * 24 * 60 * 60 * 1000, // 30 ngày
                sameSite: 'lax',
            });
            req['sessionId'] = sessionId;
        } else {
            req['sessionId'] = req.cookies.sessionId;
        }
        next();
    }
}