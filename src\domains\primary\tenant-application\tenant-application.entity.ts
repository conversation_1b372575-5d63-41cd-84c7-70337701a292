import { Entity, PrimaryColumn, Column, Index } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * Mapping tenant với application để check xem tenant có quyền truy cập vào application không
 */
@Entity('tenant_application')
@Index(['tenantId', 'applicationId'], { unique: true })
export class TenantApplicationEntity extends PrimaryBaseEntity {
    @PrimaryColumn({ type: 'uuid' })
    tenantId: string;

    @PrimaryColumn({ type: 'uuid' })
    applicationId: string;

    @ApiPropertyOptional({
        description:
            'Client ID sẽ được áp dụng khi các công ty khác muốn tích hợp vào ứng dụng của APE TECH',
    })
    @Column({ nullable: true })
    clientId?: string; // Duy nhất cho mỗi tenant-app

    @ApiPropertyOptional({
        description:
            'Client Secret sẽ được áp dụng khi các công ty khác muốn tích hợp vào ứng dụng của APE TECH',
    })
    @Column({ nullable: true })
    clientSecret?: string;

    @ApiPropertyOptional({
        description:
            'Redirect URI sẽ được áp dụng khi các công ty khác muốn tích hợp vào ứng dụng của APE TECH',
    })
    @Column({ type: 'text', array: true, nullable: true })
    redirectUris?: string[];

    @Column({ default: true })
    isActive: boolean;

    @Column({ length: 50, default: 'default' })
    plan: string;

    @ApiPropertyOptional({
        description: 'Tên miền của tenant',
    })
    @Column({ type: 'text', array: true, nullable: true })
    tenantDomain?: string[];
}
