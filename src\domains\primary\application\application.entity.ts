import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Entity, Column, BeforeInsert, BeforeUpdate } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { systemHelper } from '~/common/helpers';
import { NSAccount, NSApplication } from '~/common/enums';

@Entity('application')
export class ApplicationEntity extends PrimaryBaseEntity {
    @ApiProperty()
    @Column({ unique: true })
    code: string; // VD: "CRM", "APE_CHAIN"

    @ApiProperty()
    @Column({ length: 255 })
    name: string; // Tên hiển thị app

    @Column({ unique: true })
    clientId: string;

    @Column({ unique: true })
    clientSecret: string;

    @Column({ type: 'text', array: true })
    redirectUris: string[];

    @ApiPropertyOptional()
    @Column({ type: 'text', nullable: true })
    description: string;

    @ApiPropertyOptional()
    @Column({ default: NSApplication.EStatus.ACTIVE })
    status: NSApplication.EStatus;

    @ApiPropertyOptional({
        description:
            '<PERSON>ại tài kho<PERSON>n được phép truy cập, nếu null hoặc mảng rỗng thì là cho tất cả truy cập',
    })
    @Column({ type: 'simple-array', nullable: true })
    allowedAccountTypes: NSAccount.EType[];
}
