import { Price } from 'aws-sdk/clients/route53domains';
import {
    EntitySubscriberInterface,
    EventSubscriber,
    InsertEvent,
    RemoveEvent,
    UpdateEvent,
} from 'typeorm';
import { TransactionCommitEvent } from 'typeorm/subscriber/event/TransactionCommitEvent';
import { TransactionRollbackEvent } from 'typeorm/subscriber/event/TransactionRollbackEvent';
import { TransactionStartEvent } from 'typeorm/subscriber/event/TransactionStartEvent';
import { ExampleEntity } from './example.entity';
import tenantAccountSession from '~/modules/tenant/@guards/tenant-account/tenant-account.session';
import { loggerHelper } from '~/common/helpers';

@EventSubscriber()
export class ExampleSubscriber implements EntitySubscriberInterface {
    /**
     * Indicates that this subscriber only listen to primary events.
     */
    listenTo() {
        return ExampleEntity;
    }

    /**
     * Called after entity is loaded.
     */
    afterLoad(entity: any) {
        // console.log(`AFTER ENTITY LOADED: `);
    }

    /**
     * Called before entity insertion.
     */
    async beforeInsert(event: InsertEvent<ExampleEntity>) {
        const { entity, metadata } = event;
        loggerHelper.info(['BEFORE ENTITY INSERT: ', metadata?.name]);
        try {
            const username = tenantAccountSession?.account?.username;
            if (username) {
                entity.createdBy = username;
            }
        } catch (error) {}
    }
    /**
     * Called before entity update.
     */
    async beforeUpdate(event: UpdateEvent<ExampleEntity>) {
        const { metadata } = event;
        const entity = event?.entity || event?.databaseEntity;
        loggerHelper.info([
            'BEFORE ENTITY UPDATE entity [',
            metadata?.name,
            '] with ID: ',
            entity?.id,
        ]);
        try {
            const username = tenantAccountSession?.account?.username;
            if (username) {
                entity.updatedBy = username;
            }
        } catch (error) {}
    }

    async afterInsert(event: InsertEvent<ExampleEntity>) {
        const { entity, manager, metadata } = event;
        loggerHelper.info([
            'AFTER ENTITY INSERT entity [',
            metadata?.name,
            '] with ID: ',
            entity?.id,
        ]);
    }
    afterUpdate(event: UpdateEvent<ExampleEntity>) {
        const { entity, metadata } = event;
        loggerHelper.info([
            'AFTER ENTITY UPDATE entity [',
            metadata?.name,
            '] with ID: ',
            entity?.id,
        ]);
    }

    /**
     * Called before entity removal.
     */
    beforeRemove(event: RemoveEvent<any>) {
        // console.log(`BEFORE ENTITY WITH ID ${event.entityId} REMOVED: `);
    }

    /**
     * Called after entity removal.
     */
    afterRemove(event: RemoveEvent<any>) {
        //  console.log(`AFTER ENTITY WITH ID ${event.entityId} REMOVED: `);
    }

    /**
     * Called before transaction start.
     */
    beforeTransactionStart(event: TransactionStartEvent) {
        // console.log(`BEFORE TRANSACTION STARTED: `);
    }

    /**
     * Called after transaction start.
     */
    afterTransactionStart(event: TransactionStartEvent) {
        // console.log(`AFTER TRANSACTION STARTED: `);
    }

    /**
     * Called before transaction commit.
     */
    beforeTransactionCommit(event: TransactionCommitEvent) {
        // console.log(`BEFORE TRANSACTION COMMITTED: `);
    }

    /**
     * Called after transaction commit.
     */
    afterTransactionCommit(event: TransactionCommitEvent) {
        // console.log(`AFTER TRANSACTION COMMITTED: `);
    }

    /**
     * Called before transaction rollback.
     */
    beforeTransactionRollback(event: TransactionRollbackEvent) {
        // console.log(`BEFORE TRANSACTION ROLLBACK: `);
    }

    /**
     * Called after transaction rollback.
     */
    afterTransactionRollback(event: TransactionRollbackEvent) {
        // console.log(`AFTER TRANSACTION ROLLBACK: `);
    }
}
