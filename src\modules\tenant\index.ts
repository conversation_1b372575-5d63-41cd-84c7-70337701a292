import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule, lazyLoadClasses } from 'nestjs-typeorm3-kit';
import { PREFIX_MODULE } from '../config-module';
import { TenantAccountModule } from './@guards/tenant-account';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { join } from 'path';

const controllers = lazyLoadClasses(join(__dirname), ['.controller']);
const services = lazyLoadClasses(join(__dirname), ['.service']);

@ChildModule({
    prefix: PREFIX_MODULE.tenant,
    imports: [PrimaryRepoModule, TenantAccountModule],
    providers: [...services],
    controllers: [...controllers],
})
export class TenantModule implements NestModule {
    configure(consumer: MiddlewareConsumer) {}
}
