import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions, LoggerOptions } from 'typeorm';
import { addTransactionalDataSource } from 'typeorm-transactional';
import { parserHelper } from '~/common/helpers';

export interface DatabaseConfig {
    name: string;
    type: DataSourceOptions['type'];
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
    synchronize: boolean;
    ssl: boolean;
    sslRejectUnauthorized: boolean;
    entities: DataSourceOptions['entities'];
    subscribers?: DataSourceOptions['subscribers'];
    migrations?: DataSourceOptions['migrations'];
    migrationsRun?: DataSourceOptions['migrationsRun'];
    logging?: LoggerOptions;
    logger?: DataSourceOptions['logger'];
}

export const initConnection = (config: DatabaseConfig) => {
    if (!config.type) {
        throw new Error('TYPE is required');
    }
    const convertSslOption = () => {
        if (config.type === 'postgres') {
            return {
                ssl: parserHelper.stringToBoolean(config.ssl)
                    ? {
                          rejectUnauthorized: parserHelper.stringToBoolean(
                              config.sslRejectUnauthorized,
                          ),
                      }
                    : undefined,
            };
        }
        if (config.type === 'mssql') {
            return { options: { encrypt: false } };
        }
        if (config.type === 'mysql') {
            return { ssl: parserHelper.stringToBoolean(config.ssl) };
        }
        return { ssl: parserHelper.stringToBoolean(config.ssl) };
    };
    const sslOption = convertSslOption();
    return TypeOrmModule.forRootAsync({
        name: config.name,
        useFactory: () => ({
            type: config.type as any,
            host: config.host,
            port: Number(config.port),
            username: config.username,
            password: config.password,
            database: config.database,
            synchronize: parserHelper.stringToBoolean(config.synchronize),
            entities: config.entities,
            subscribers: config?.subscribers,
            migrations: config?.migrations,
            migrationsRun: config?.migrationsRun,
            logging: config?.logging,
            logger: config?.logger,
            ...sslOption,
        }),
        dataSourceFactory: async (options: DataSourceOptions) => {
            if (!options) {
                throw new Error('Invalid options passed');
            }
            return addTransactionalDataSource({
                dataSource: new DataSource(options),
                name: config.name,
            });
        },
    });
};
