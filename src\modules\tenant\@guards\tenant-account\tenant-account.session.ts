import { RequestContext } from '~/@core/context';
import { KEY_SESSION_CONTEXT } from '~/common/constants';
import { TenantAccountSessionDto } from './dto';

class TenantAccountSession {
    get sessionData() {
        return RequestContext.getAttribute<TenantAccountSessionDto>(
            KEY_SESSION_CONTEXT.TENANT_ACCOUNT_SESSION,
        );
    }
    get account() {
        return this.sessionData?.account;
    }
    get tenantId() {
        return this.account?.tenantId;
    }
    get application() {
        return this.sessionData?.application;
    }
    get memberId() {
        return this.sessionData?.sub;
    }
    get applicationId() {
        return this?.application?.id;
    }
}

export default new TenantAccountSession();
