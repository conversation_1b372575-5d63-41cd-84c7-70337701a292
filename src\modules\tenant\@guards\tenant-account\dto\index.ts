import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import * as jwt from 'jsonwebtoken';
import { AccountEntity } from '~/domains/primary';
import { ApplicationEntity } from '~/domains/primary';

export class JwtPayload implements jwt.JwtPayload {
    @ApiPropertyOptional()
    iss?: string | undefined;
    @ApiPropertyOptional()
    sub?: string | undefined;
    @ApiPropertyOptional()
    aud?: string | string[] | undefined;
    @ApiPropertyOptional()
    exp?: number | undefined;
    @ApiPropertyOptional()
    nbf?: number | undefined;
    @ApiPropertyOptional()
    iat?: number | undefined;
    @ApiPropertyOptional()
    jti?: string | undefined;
}

export class TenantAccountPayload extends JwtPayload {
    @ApiProperty()
    username: string;

    @ApiProperty()
    applicationId: string;
}

export class TenantAccountDto extends TenantAccountPayload {
    @ApiProperty()
    accessToken: string;
    @ApiProperty()
    refreshToken: string;
    @ApiProperty()
    tokenType: 'Bearer' = 'Bearer';
}

export class TenantAccountSessionDto extends TenantAccountPayload {
    @ApiProperty()
    account: Omit<AccountEntity, 'password'>;
    @ApiProperty()
    application: ApplicationEntity;
}
