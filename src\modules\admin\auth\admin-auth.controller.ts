import { Def<PERSON><PERSON>roller, DefGet, DefPost } from "nestjs-typeorm3-kit";
import { ListAdminDto, LoginAdmin, RegisterAdmin, ResetPasswordDto, UpdateInfoDto } from "./dto/index.dto";
import { Body, Query } from "@nestjs/common";
import { AdminAuthService } from "./admin-auth.service";

@DefController("auth")
export class AdminAuthController {
    constructor(private readonly adminAuthService: AdminAuthService) { }

    @DefPost("register")
    async register(@Body() data: RegisterAdmin) {
        return this.adminAuthService.createAdminAccount(data);
    }

    // Login
    @DefPost("login")
    async login(@Body() data: LoginAdmin) {
        return this.adminAuthService.login(data);
    }

    // List
    @DefGet("list")
    async getList(@Query() queries: ListAdminDto) {
        return this.adminAuthService.list(queries);
    }

    // Update info
    @DefPost("update")
    async updateInfo(@Body() body: UpdateInfoDto) {
        return this.adminAuthService.updateInfo(body);
    }

    @DefPost("active")
    async active(@Body() body: UpdateInfoDto) {
        return this.adminAuthService.activeUser(body);
    }

    @DefPost("inactive")
    async inActive(@Body() body: UpdateInfoDto) {
        return this.adminAuthService.inActiveUser(body);
    }

    @DefPost("reset-password")
    async resetPassword(@Body() body: ResetPasswordDto) {
        return this.adminAuthService.resetPassword(body);
    }
}