import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PageRequest } from '~/@systems/utils';
import { NSApplication } from '~/common/enums';

export class RegisterApplicationReq {
    @ApiProperty({ example: 'CRM' })
    code: string;
}

export class UnregisterApplicationReq {
    @ApiProperty({ example: 'CRM' })
    code: string;
}

export class ListApplicationReq extends PageRequest {
    @ApiPropertyOptional()
    keyword?: string;
    @ApiPropertyOptional()
    status?: NSApplication.EStatus;
}

export class ListRegisteredApplicationReq {
    @ApiPropertyOptional()
    keyword?: string;
}

export class ListAccountRegisteredByApplicationReq {
    @ApiPropertyOptional()
    applicationId: string;
}
