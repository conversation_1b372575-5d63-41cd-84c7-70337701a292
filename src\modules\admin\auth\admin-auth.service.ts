import { Inject, Injectable } from "@nestjs/common";
import { DefTransaction, InjectRepo } from "nestjs-typeorm3-kit";
import { UserAdminRepo } from "~/domains/primary";
import { ListAdminDto, LoginAdmin, RegisterAdmin, ResetPasswordDto, UpdateInfoDto } from "./dto/index.dto";
import { JwtService } from '@nestjs/jwt';
import securityHelper from "~/@core/helpers/security.helper";
import { BusinessException } from "~/@systems/exceptions";
import { configEnv } from '~/@config/env';
import { UserAdminEntity } from "~/domains/primary";
import { NSAccount } from "~/common/enums";

@Injectable()
export class AdminAuthService {
    constructor(
        private jwtService: JwtService,
        @InjectRepo(UserAdminRepo)
        private readonly userAdminRepo: UserAdminRepo
    ) { }

    @DefTransaction()
    async createAdminAccount(data: RegisterAdmin) {
        const hashedPassword = await securityHelper.hash(data.password);
        const admin = this.userAdminRepo.create({ ...data, password: hashedPassword });
        return this.userAdminRepo.save(admin);
    }

    private async generateRefreshToken(userId: string) {
        const { JWT_REFRESH_TOKEN_EXPIRY, JWT_REFRESH_TOKEN_SECRET } = configEnv();
        const newRefreshToken = await this.jwtService.signAsync(
            { sub: userId },
            {
                secret: JWT_REFRESH_TOKEN_SECRET,
                expiresIn: JWT_REFRESH_TOKEN_EXPIRY,
            },
        );

        return newRefreshToken;
    }

    private clearPrivateMemberData(admin: UserAdminEntity) {
        const { password, createdBy, updatedBy, createdDate, updatedDate, ...rest } = admin;
        return rest;
    }

    private async makeAuthResponse(admin: UserAdminEntity) {
        const pipeMember = this.clearPrivateMemberData(admin);
        const payload = {
            sub: admin.id,
            ...pipeMember,
        };
        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.generateRefreshToken(admin.id),
            tokenType: 'Bearer',
            ...pipeMember,
        };
    }

    //login
    async login(body: LoginAdmin) {
        const userAdmin = await this.userAdminRepo.findOne({
            where: {
                username: body.username,
            },
        });

        if (!userAdmin) {
            throw new BusinessException('admin_auth.login.error.member_not_existed');
        }

        const checkPass = await securityHelper.compare(body?.password, userAdmin.password);
        if (!checkPass) {
            throw new BusinessException('admin_auth.login.error.wrong_password');
        }
        if(userAdmin.status !== NSAccount.EStatus.ACTIVE) {
             throw new BusinessException('admin_auth.login.error.inactive');
        }
        return this.makeAuthResponse(userAdmin);
    }

    //list
    async list(query: ListAdminDto) {
        const { username, status, ...rest } = query;
        const wheres: any = {}
        if (username) {
            wheres.username = username
        }
        if (status) {
            wheres.status = status
        }
        return this.userAdminRepo.findPagination(
            {
                where: wheres,
                select: ["id", "fullName", "username", "avatar", "type", "version", "createdDate", "updatedDate", "status"],
                order: {createdDate: 'DESC'}
            },
            { ...rest }
        )
    }

    //update info
    @DefTransaction()
    async updateInfo(body: UpdateInfoDto) {
        const { id } = body;
        const check = await this.userAdminRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException("auth.update.not_found_user")
        }
        return await this.userAdminRepo.save(body);

    }

    //Active
    @DefTransaction()
    async activeUser(body: UpdateInfoDto) {
        const { id } = body;
        const check = await this.userAdminRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException("auth.update.not_found_user")
        }
        return await this.userAdminRepo.update(
            { id },
            { status: NSAccount.EStatus.ACTIVE }
        )
    }

    //Active
    @DefTransaction()
    async inActiveUser(body: UpdateInfoDto) {
        const { id } = body;
        const check = await this.userAdminRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException("auth.update.not_found_user")
        }
        return await this.userAdminRepo.update(
            { id },
            { status: NSAccount.EStatus.INACTIVE }
        )
    }

    @DefTransaction()
    async resetPassword(body: ResetPasswordDto) {
        const { id, password, passwordConfirm } = body;
        const check = await this.userAdminRepo.findOne({ where: { id } });
        if (!check) {
            throw new BusinessException("auth.update.not_found_user")
        }
        if(password !== passwordConfirm) {
            throw new BusinessException("auth.resetpassword.wrong_password_confirm")
        }
        const hashedPassword = await securityHelper.hash(password);
        await this.userAdminRepo.update({id}, {password: hashedPassword});
    }
}