<!DOCTYPE html>
<html>
<head>
  <title><PERSON><PERSON><PERSON> nhập SSO</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      background: linear-gradient(90deg, rgb(2, 0, 36) 0%, rgb(9, 9, 121) 35%, rgb(0, 212, 255) 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: 'Segoe UI', Arial, sans-serif;
    }
    .card {
      background: #fff;
      padding: 32px;
      border-radius: 14px;
      box-shadow: 0 8px 32px 0 rgba(60,60,80,0.08), 0 1.5px 5px 0 rgba(0,0,0,0.06);
      width: 340px;
      max-width: 90vw;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .card h2 {
      margin-top: 0;
      margin-bottom: 24px;
      color: #2d3748;
      font-size: 1.4rem;
      font-weight: 600;
      letter-spacing: .03em;
    }
    .error {
      background: #ffe7e7;
      color: #b30000;
      padding: 8px 12px;
      border-radius: 6px;
      margin-bottom: 16px;
      width: 100%;
      text-align: center;
      font-size: .98rem;
    }
    form {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }
    label {
      color: #222;
      font-size: .96rem;
      margin-bottom: 2px;
      font-weight: 500;
    }
    input[type="text"], input[type="password"] {
      width: 100%;
      padding: 10px 4px;
      border-radius: 7px;
      border: 1px solid #d1d5db;
      font-size: 1rem;
      outline: none;
      transition: border .2s;
      margin-top: 3px;
      background: #f9f9fb;
    }
    input[type="text"]:focus, input[type="password"]:focus {
      border: 1.5px solid #6299ff;
      background: #fff;
    }
    button {
      width: 100%;
      padding: 11px 0;
      border: none;
      border-radius: 7px;
      background: #2178fb;
      color: #fff;
      font-weight: 600;
      font-size: 1.08rem;
      cursor: pointer;
      margin-top: 6px;
      transition: background .18s;
    }
    button:hover {
      background: #1664c1;
    }
    .powered {
      font-size: .80rem;
      color: #999;
      margin-top: 18px;
    }
    img {
      width: 80px;
      height: 80px;
      object-fit: contain;
    }
  </style>
</head>
<body>
  <div class="card">
    <img src="https://ape-devs-co.s3.ap-southeast-1.amazonaws.com/kQX0BgUMhH" />
    <h2>APE Authenticator</h2>
    {{#if error}}<div class="error">{{error}}</div>{{/if}}
    <form method="POST" action="/api/client/auth/authorize?{{queryStringUrl}}">
      <input type="hidden" name="redirect_uri" value="{{redirectUri}}" />
      <input type="hidden" name="client_id" value="{{clientId}}" />
      <input type="hidden" name="state" value="{{state}}" />

      <label for="username">Tên đăng nhập</label>
      <input id="username" name="username" type="text" required autofocus autocomplete="username" />

      <label for="password">Mật khẩu</label>
      <input id="password" name="password" type="password" required autocomplete="current-password" />

      <button type="submit">Đăng nhập</button>
    </form>
    <div class="powered">Powered by APE Tech Solutions</div>
  </div>
</body>
</html>
