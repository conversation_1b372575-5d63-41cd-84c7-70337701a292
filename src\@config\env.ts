import { parserHelper } from '~/common/helpers';

export function configEnv() {
    return {
        PORT: Number(process.env.PORT),
        TZ: process.env.TZ,
        REQUEST_TIMEOUT: Number(process.env.REQUEST_TIMEOUT),
        DB_PRIMARY_TYPE: process.env.DB_PRIMARY_TYPE,
        DB_PRIMARY_HOST: process.env.DB_PRIMARY_HOST,
        DB_PRIMARY_PORT: Number(process.env.DB_PRIMARY_PORT),
        DB_PRIMARY_USERNAME: process.env.DB_PRIMARY_USERNAME,
        DB_PRIMARY_PASSWORD: process.env.DB_PRIMARY_PASSWORD,
        DB_PRIMARY_DATABASE: process.env.DB_PRIMARY_DATABASE,
        DB_PRIMARY_SYNCHRONIZE: parserHelper.stringToBoolean(process.env.DB_PRIMARY_SYNCHRONIZE),
        DB_PRIMARY_SSL: parserHelper.stringToBoolean(process.env.DB_PRIMARY_SSL),
        DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: parserHelper.stringToBoolean(
            process.env.DB_PRIMARY_SSL_REJECT_UNAUTHORIZED,
        ),
        // SWAGGER CONFIG
        SWAGGER_TITLE: process.env.SWAGGER_TITLE,
        SWAGGER_DESCRIPTION: process.env.SWAGGER_DESCRIPTION,
        SWAGGER_VERSION: process.env.SWAGGER_VERSION,
        JWT_SECRET: process.env.JWT_SECRET,
        JWT_EXPIRY: process.env.JWT_EXPIRY,
        JWT_REFRESH_TOKEN_SECRET: process.env.JWT_REFRESH_TOKEN_SECRET,
        JWT_REFRESH_TOKEN_EXPIRY: process.env.JWT_REFRESH_TOKEN_EXPIRY,
        PUBLIC_API_KEY: process.env.PUBLIC_API_KEY
    } as NodeJS.ProcessEnv;
}
