import { Injectable } from '@nestjs/common';
import { DefTransaction, InjectRepo } from 'nestjs-typeorm3-kit';
import {
    AccountApplicationRepo,
    AccountEntity,
    ApplicationRepo,
    TenantApplicationRepo,
    TenantEntity,
    TenantRepo,
} from '~/domains/primary';
import { AccountRepo } from '~/domains/primary/account/account.repo';
import {
    RegisterAccount,
    UpdateAccountDto,
    UpdatePasswordDto,
    UpdateTenantDto,
} from './dto/index.dto';
import { NSAccount } from '~/common/enums/account.enum';
import { BusinessException } from '~/@systems/exceptions';
import securityHelper from '~/@core/helpers/security.helper';
import { TenantService } from '~/modules/admin/tenant/tenant.service';
import { In } from 'typeorm';
import { NSTenant } from '~/common/enums';
import { generateCodeHelper, systemHelper } from '~/common/helpers';
import { DataSource } from 'typeorm';
import { chunk, limitConcurrency } from '~/common/helpers/syns.helper';
import { ResetPasswordAccountDto } from '~/modules/admin/account/dto/account.dto';
const PG_UNIQUE_VIOLATION = '23505';
const CONCURRENCY_LIMIT = 10; // chỉnh tùy DB

@Injectable()
export class AccountService {
    constructor(
        private readonly tenantService: TenantService,
        @InjectRepo(AccountRepo)
        private readonly accountRepo: AccountRepo,
        @InjectRepo(TenantRepo)
        private readonly tenantRepo: TenantRepo,
        @InjectRepo(TenantApplicationRepo)
        private readonly tenantAppRepo: TenantApplicationRepo,
        @InjectRepo(ApplicationRepo)
        private readonly applicationRepo: ApplicationRepo,
        @InjectRepo(AccountApplicationRepo)
        private readonly accountAppRepo: AccountApplicationRepo,
        private readonly dataSource: DataSource,
    ) {}

    @DefTransaction()
    async registerAccount(body: RegisterAccount) {
        const password = await securityHelper.hash(body.password);
        if (body.type === NSAccount.EType.TENANT_USER) {
            const tenant = await this.tenantRepo.findOne({ where: { id: body.tenantId } });
            if (!tenant) throw new BusinessException('tenant.not_found');
            const domain = tenant.domain;
            const application = await this.applicationRepo.findOne({
                where: { clientId: body.clientId },
            });

            const userName = systemHelper.generateUserNameAccount(body.username, domain);
            const account = await this.accountRepo.save({
                ...body,
                username: userName,
                password,
            });
            await this.accountAppRepo.save({
                accountId: account.id,
                applicationId: application.id,
                tenantId: tenant.id,
            });

            delete account.password;
            return account;
        }
        // ROOT
        else if (body.type === NSAccount.EType.TENANT_MASTER) {
            if (!body.domain) throw new BusinessException('tenant.domain_required');
            if (!body.tax) throw new BusinessException('tenant.tax_code_required');
            if (!body.username) throw new BusinessException('tenant.username_required');
            if (!body.password) throw new BusinessException('tenant.password_required');
            const rs = await this.tenantService.register({
                domain: body.domain,
                tenantDomain: `${body.domain}-example.ape.com`,
                taxCode: body.tax,
                rootUsername: body.username,
                rootPassword: body.password,
                name: body.fullName,
                rootFullName: body.fullName,
                website: body.website,
                address: body.address,
                phone: body.phone,
                email: body.email,
            });
            return rs;
        }
        // Đăng ký khách hàng của ROOT + Dựa vào ClientId + Domain từ phía phía client
        else {
            // TODO 1. Nếu là khách hàng của ROOT
            // TODO 2. Thì sinh Tenant + account nhân viên không phải kiểu root
            // TODO 3. Nếu taxCode của Tenant đã tồn tại thì không tạo Tenant nữa
            // TODO 4. Prefix của account sẽ đi theo domain của thằng Root
            // TODO 5. Trả về account info đã tạo, không trả về mật khẩu

            const { clientId, host, tax, domain } = body;
            //Check clientId trong Tenant Application
            const tenantApp = await this.tenantAppRepo.findOne({ where: { clientId: clientId } });
            if (!tenantApp) {
                //#region xử lý của a Vũ

                // Tìm Client ID trong Application
                const application = await this.applicationRepo.findOne({
                    where: { clientId: clientId },
                });
                if (!application) throw new BusinessException('application.not_found');

                // const tenant = await this.tenantRepo.findOne({ where: { taxCode: tax } });
                // if (!tenant) {
                //     const newTenant = await this.tenantRepo.save({
                //         ...body,
                //         taxCode: tax,
                //         domain: domain,
                //         status: NSTenant.EStatus.ACTIVE,
                //     });

                //     const account = await this.createAccount(newTenant, body);
                //     return account;
                // } else {
                //     const account = await this.createAccount(tenant, body);
                //     return account;
                // }

                //#endregion

                //#region xử lý của Dũng

                let tenant = await this.tenantRepo.findOne({ where: { taxCode: tax } });
                if (!tenant) {
                    tenant = await this.tenantRepo.save({
                        ...body,
                        taxCode: tax,
                        domain: domain,
                        status: NSTenant.EStatus.ACTIVE,
                    });
                }

                const tenantApps = await this.tenantAppRepo.find({
                    where: { applicationId: application.id },
                });

                const tenantApp = tenantApps.find(app => {
                    if (!app.tenantDomain || app.tenantDomain.length === 0) return false;

                    const domains = app.tenantDomain.flatMap(d => d.split(',').map(s => s.trim()));

                    return domains.includes(host);
                });

                if (!tenantApp) throw new BusinessException('Tenant not found for this host');

                tenant = await this.tenantRepo.findOne({ where: { id: tenantApp.tenantId } });
                if (!tenant) throw new BusinessException('Tenant not found');
                const account = await this.accountRepo.findOne({
                    where: {
                        tenantId: tenant.id,
                        status: NSAccount.EStatus.ACTIVE,
                    },
                });
                if (!account) throw new BusinessException('Account Not Found');

                // Tạo account theo tenant root
                const createdAccount = await this.createAccount(tenant, body);
                await this.accountAppRepo.save({
                    accountId: createdAccount.id,
                    applicationId: application.id,
                    tenantId: tenant.id,
                });

                return createdAccount;

                //#endregion
            } else {
                const tenant = await this.tenantRepo.findOne({ where: { id: tenantApp.tenantId } });
                if (!tenant) throw new BusinessException('tenant.not_found');

                //Check tax xem đã tồn tại trên Tenant chưa
                const existingTenant = await this.tenantRepo.findOne({ where: { taxCode: tax } });
                if (!existingTenant) {
                    // Nếu chưa có thì tạo Tenant
                    // Xử lý domain
                    const newDomain = domain
                        ? domain
                        : systemHelper.generateDomainByName(body.fullName);
                    const newTenant = await this.tenantRepo.save({
                        ...body,
                        taxCode: tax,
                        domain: newDomain,
                        status: NSTenant.EStatus.ACTIVE,
                    });
                }

                const userName = systemHelper.generateUserNameAccount(body.username, tenant.domain);
                const password = await securityHelper.hash(body.password);
                const account = await this.accountRepo.save({
                    ...body,
                    username: userName,
                    password,
                    tenantId: tenant.id,
                    type: NSAccount.EType.CUSTOMER,
                    status: NSAccount.EStatus.ACTIVE,
                });
                delete account.password;
                return account;
            }
        }
    }

    @DefTransaction()
    async createAccount(tenant: any, body: any) {
        const userName = systemHelper.generateUserNameAccount(body.username, tenant.domain);
        const password = await securityHelper.hash(body.password);
        const account = await this.accountRepo.save({
            ...body,
            username: userName,
            password,
            tenantId: tenant.id,
            type: NSAccount.EType.CUSTOMER,
            status: NSAccount.EStatus.ACTIVE,
        });
        delete account.password;
        return account;
    }

    @DefTransaction()
    async updateAccount(body: UpdateAccountDto) {
        const { accountId } = body;
        const account = await this.accountRepo.findOne({ where: { id: accountId } });
        if (!account) throw new BusinessException('account.not_found');
        return await this.accountRepo.update(
            {
                id: accountId,
            },
            {
                ...body,
            },
        );
    }

    @DefTransaction()
    async updateTenant(body: UpdateTenantDto) {
        const { tenantId } = body;
        const tenant = await this.tenantRepo.findOne({ where: { id: tenantId } });
        if (!tenant) throw new BusinessException('tenant.not_found');

        if (body.taxCode) {
            const existingTenant = await this.tenantRepo.findOne({
                where: { taxCode: body.taxCode },
            });
            if (existingTenant) throw new BusinessException('tenant.tax_code_exists');
        }
        if (body.domain) {
            const existingTenant = await this.tenantRepo.findOne({
                where: { domain: body.domain },
            });
            if (existingTenant) throw new BusinessException('tenant.domain_exists');

            const accounts = await this.accountRepo.find({ where: { tenantId } });
            for (const account of accounts) {
                account.username = systemHelper.generateUserNameAccount(
                    account.username,
                    body.domain,
                );
                await this.accountRepo.update({ id: account.id }, account);
            }
        }
        delete body.tenantId;
        return await this.tenantRepo.update(
            {
                id: tenantId,
            },
            {
                ...body,
            },
        );
    }

    @DefTransaction()
    async updatePassword(body: UpdatePasswordDto) {
        const { accountId, oldPassword, newPassword, confirmPassword } = body;
        const account = await this.accountRepo.findOne({ where: { id: accountId } });
        if (!account) throw new BusinessException('account.not_found');
        // check old password
        const isMatch = await securityHelper.compare(oldPassword, account.password);
        if (!isMatch) throw new BusinessException('account.old_password_incorrect');
        if (newPassword !== confirmPassword)
            throw new BusinessException('account.password_not_match');
        const hashedPassword = await securityHelper.hash(newPassword);
        return await this.accountRepo.update(
            { id: accountId },
            {
                password: hashedPassword,
            },
        );
    }

    @DefTransaction()
    async getAccountsWithPMS() {
        const accounts = await this.accountRepo.find({
            where: {
                username: In(
                    (await this.accountRepo.find())
                        .filter(acc => acc.username.includes('@PMS'))
                        .map(acc => acc.username),
                ),
            },
        });

        accounts.forEach(acc => delete acc.password);
        return accounts;
    }
    
    async syncSuppliers(clientId: string, data: RegisterAccount[]) {
        const application = await this.applicationRepo.findOne({ where: { clientId } });
        if (!application) throw new BusinessException('application.not_found');

        // “Preview” nếu bạn vẫn muốn:
        const prepared = data.map(t => ({
            ...t,
            tenantDomain: `${t.domain}-example.ape.com`,
        }));

        // Lọc tax hợp lệ
        const taxCodes = prepared
            .map(x => x.tax?.trim())
            .filter((x): x is string => !!x && x.length > 0);

        // Tìm tax đã tồn tại (chunk tránh IN quá dài)
        const existingTaxSet = new Set<string>();
        for (const c of chunk(taxCodes, 5000)) {
            const existed = await this.tenantRepo.find({
                select: { taxCode: true },
                where: { taxCode: In(c) },
            });
            existed.forEach(t => existingTaxSet.add(t.taxCode));
        }

        const tasks = prepared.map((item, index) => {
            const tax = item.tax?.trim();
            if (!tax) return { index, item, action: 'SKIP' as const };
            if (existingTaxSet.has(tax)) return { index, item, action: 'SKIP' as const };
            return { index, item, action: 'CREATE' as const };
        });

        const skipped = [];
        const errors = [];
        const okTenants: any[] = [];
        const okAccounts: any[] = [];

        // Ghi nhận SKIP
        for (const t of tasks) {
            const tax = t.item.tax?.trim();
            if (t.action === 'SKIP') {
                if (!tax) errors.push({ index: t.index, message: 'Missing tax' });
                else skipped.push({ index: t.index, tax, reason: 'EXISTS' });
            }
        }

        const toCreate = tasks.filter(t => t.action === 'CREATE');

        // Worker luôn “ăn” lỗi, không throw ra ngoài limitConcurrency
        const worker = async (t: { index: number; item: RegisterAccount }) => {
            try {
                const { tenant, account } = await this.createOneTenantAndAccountWithRetry(
                    application.id,
                    t.item,
                );
                okTenants.push(tenant);
                okAccounts.push(account);
            } catch (e: any) {
                errors.push({
                    index: t.index,
                    tax: t.item.tax,
                    message: e?.message || 'Unknown error',
                });
            }
        };

        await limitConcurrency(toCreate, worker, CONCURRENCY_LIMIT);

        return {
            summary: {
                input: data.length,
                toCreate: toCreate.length,
                skippedExisting: skipped.length,
                succeeded: okTenants.length,
                failed: errors.length,
            },
            data: okTenants,
            skipped,
            errors,
        };
    }

    @DefTransaction()
    async createTenant(applicationId: string, body: RegisterAccount[]) {
        const taxCodes = body.map(item => item.tax);
        // Kiểm tra taxCodes, nếu đã tồn tại thì bỏ qua không tạo mới
        const existingTenants = await this.tenantRepo.find({ where: { taxCode: In(taxCodes) } });
        const existingTaxCodes = existingTenants.map(item => item.taxCode);
        const newTenants = body.filter(item => !existingTaxCodes.includes(item.tax));

        const newData = [];
        for (const tenant of newTenants) {
            newData.push({
                ...tenant,
                taxCode: tenant.tax,
                // Không có domain thì tự generate
                domain: tenant.domain
                    ? tenant.domain
                    : systemHelper.generateDomainByName(tenant.fullName),
                status: NSTenant.EStatus.INACTIVE,
            });
        }

        const domains = newData.map(item => item.domain);
        // Nếu domain đã tồn tại trong databse thì nối chuỗi "-random text"
        const uniqueDomains = domains.map(domain => {
            const existingTenant = newTenants.find(item => item.domain === domain);
            if (existingTenant) {
                return `${domain}-${generateCodeHelper.generateReferralCode().toLowerCase()}`;
            }
            return domain;
        });

        for (let i = 0; i < newData.length; i++) {
            newData[i].domain = uniqueDomains[i];
        }

        const app = await this.applicationRepo.findOne({ where: { id: applicationId } });
        if (!app) throw new BusinessException('application.not_found');

        const rs = await this.tenantRepo.save(newData);
        // Tạo account type CUSTOMER theo danh sách tenant mới
        const newAccount = [];
        for (const tenant of rs) {
            // Không có user name thì sinh tự động
            const username = tenant.username
                ? tenant.username
                : systemHelper.generateUserNameAccount(`cus-${tenant.taxCode}`, tenant.domain);
            // Không có password thì default **********
            const password = tenant.password ? tenant.password : '**********';
            newAccount.push({
                tenantId: tenant.id,
                username,
                fullName: `Khách hàng ${tenant.name}`,
                password: await securityHelper.hash(password),
                type: NSAccount.EType.CUSTOMER,
                status: NSAccount.EStatus.ACTIVE,
            });
        }
        const newAccounts = await this.accountRepo.save(newAccount);

        // Sau khi tạo Account thì gán account vào application, chờ bảng account_application

        return {
            total: newAccounts.length,
            accounts: newAccounts,
            tenants: rs,
        };
    }

    // Sinh domain duy nhất — kiểm tra DB trước (best-effort) rồi vẫn bắt UNIQUE khi save
    private async generateUniqueDomainBase(base: string): Promise<string> {
        const normalized = base.trim().toLowerCase();
        const exists = await this.tenantRepo.findOne({ where: { domain: normalized } });
        if (!exists) return normalized;
        return `${normalized}-${generateCodeHelper.generateReferralCode().toLowerCase()}`;
    }

    // Lưu 1 tenant + account trong transaction RIÊNG, có retry khi domain trùng (23505)
    private async createOneTenantAndAccountWithRetry(
        applicationId: string,
        item: RegisterAccount,
        maxRetry = 5,
    ) {
        const app = await this.applicationRepo.findOne({ where: { id: applicationId } });
        if (!app) throw new BusinessException('application.not_found');

        const taxCode = item.tax?.trim();
        if (!taxCode) throw new BusinessException('tenant.tax_required');

        const baseDomain = (
            item.domain?.trim() ||
            systemHelper.generateDomainByName((item as any).fullName || taxCode)
        ).toLowerCase();

        let lastError: any = null;

        for (let attempt = 0; attempt < maxRetry; attempt++) {
            try {
                return await this.dataSource.transaction(async manager => {
                    // Chuẩn bị domain — best-effort check trước
                    let domain = await this.generateUniqueDomainBase(baseDomain);

                    // Tạo tenant
                    const tenantRepo = manager.getRepository(TenantEntity);
                    const tenant = tenantRepo.create({
                        ...(item as any),
                        taxCode,
                        domain,
                        status: NSTenant.EStatus.INACTIVE,
                    });

                    // Lưu tenant (có thể ném 23505 do domain/taxCode trùng)
                    const savedTenant = (await tenantRepo.save(tenant)) as unknown as TenantEntity;

                    // Tạo account CUSTOMER
                    const accountRepo = manager.getRepository(AccountEntity);
                    const username =
                        (item as any).username ||
                        systemHelper.generateUserNameAccount(
                            `cus-${savedTenant.taxCode}`,
                            savedTenant.domain,
                        );
                    const passwordPlain = (item as any).password || '**********';

                    const account = accountRepo.create({
                        tenantId: savedTenant.id,
                        username,
                        fullName: `Khách hàng ${savedTenant.name ?? savedTenant.taxCode}`,
                        password: await securityHelper.hash(passwordPlain),
                        type: NSAccount.EType.CUSTOMER,
                        status: NSAccount.EStatus.ACTIVE,
                    });
                    const savedAccount: AccountEntity = await accountRepo.save(account);

                    // TODO: gán vào application khi có bảng liên kết
                    // await manager.getRepository(AccountApplicationEntity).save({ accountId: savedAccount.id, applicationId });

                    return { tenant: savedTenant, account: savedAccount };
                });
            } catch (e: any) {
                // Nếu là UNIQUE VIOLATION ở domain hoặc tax_code thì retry (đổi hậu tố domain)
                if (e?.code === PG_UNIQUE_VIOLATION) {
                    lastError = e;
                    // đổi base để lần tới khác đi
                    // (giữ nguyên baseDomain, chỉ thêm hậu tố khác)
                    continue;
                }
                throw e;
            }
        }

        // Hết retry mà vẫn dính UNIQUE
        throw lastError || new Error('Unique constraint conflict after retries');
    }

    // User Root reset password for user, customer
    @DefTransaction()
    async resetPassword(body: ResetPasswordAccountDto) {
        const { accountId, newPassword, confirmNewPassword } = body;
        const account = await this.accountRepo.findOne({ where: { id: accountId } });
        if (!account) throw new BusinessException('account.not_found');
        if (newPassword !== confirmNewPassword)
            throw new BusinessException('account.password_not_match');
        const hashedPassword = await securityHelper.hash(newPassword);
        return await this.accountRepo.update(
            { id: accountId },
            {
                password: hashedPassword,
            },
        );
    }
}
