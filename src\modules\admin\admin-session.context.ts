import { RequestContext } from '~/@core/context';
import { KEY_SESSION_CONTEXT } from '~/common/constants';
import { AdminSessionDto } from './auth/dto/index.dto';

export class AdminSessionContext {
  get sessionData() {
    return RequestContext.getAttribute<AdminSessionDto>(KEY_SESSION_CONTEXT.ADMIN_SESSION);
  }

  get accessToken() {
    return this.sessionData?.accessToken;
  }
  get userId() {
    return this?.sessionData?.sub;
  }
  get username() {
    return this.sessionData.username;
  }
}

export const adminSessionContext = new AdminSessionContext();
