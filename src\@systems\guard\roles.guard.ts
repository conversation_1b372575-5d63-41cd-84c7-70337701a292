// roles.guard.ts
import {
    CanActivate, ExecutionContext, Injectable, ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import { ROLES_KEY } from '~/@core/decorator/role.decorator';
import { NSAccount } from '~/common/enums';
import { UserAdminRepo } from '~/domains/primary';
import { adminSessionContext } from '~/modules/admin/admin-session.context';

@Injectable()
export class RolesGuard implements CanActivate {
    constructor(
        private reflector: Reflector,
        @InjectRepo(UserAdminRepo)
        private readonly userAdminRepo: UserAdminRepo
    ) { }

    async canActivate(ctx: ExecutionContext): Promise<boolean> {
        const required = this.reflector.getAllAndOverride<NSAccount.EAdminType[]>(ROLES_KEY, [
            ctx.getHandler(),
            ctx.getClass(),
        ]);
        const { userId } = adminSessionContext;
        const userAdmin = await this.userAdminRepo.findOne({
            where: {
                id: userId
            }
        })

        // Không gắn @Roles thì không hạn chế
        if (!required || required.length === 0) return true;

        const ok =
          userAdmin.type === NSAccount.EAdminType.SUPER_ADMIN ||
          required.includes(userAdmin.type);

        if (!ok) {
          throw new ForbiddenException('User dont have permission');
        }
        return true;
    }
}
